{"name": "lullaby-clinic", "version": "1.0.0", "description": "A blank template to get started with Payload 3.0", "license": "MIT", "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS=--no-deprecation next build", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev", "devsafe": "rm -rf .next && cross-env NODE_OPTIONS=--no-deprecation next dev", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "generate:schemas": "node scripts/generateSchemas.js", "test:crud": "node scripts/runCrudTests.js", "test:supabase": "node scripts/checkSupabaseData.js", "test:media": "node scripts/testMediaUpload.js", "test:storage": "node scripts/testSupabaseStorage.js", "verify:media": "node scripts/verifyMediaSystem.js", "migrate:data": "node scripts/migrateLegacyData.js", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "start": "cross-env NODE_OPTIONS=--no-deprecation next start"}, "dependencies": {"@aws-sdk/client-s3": "^3.832.0", "@payloadcms/db-postgres": "3.43.0", "@payloadcms/next": "3.43.0", "@payloadcms/payload-cloud": "3.43.0", "@payloadcms/richtext-lexical": "3.43.0", "@payloadcms/storage-s3": "^3.43.0", "@payloadcms/ui": "3.43.0", "@radix-ui/react-slot": "^1.2.3", "@supabase/supabase-js": "^2.50.0", "@tailwindcss/postcss": "^4.1.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cross-env": "^7.0.3", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "graphql": "^16.8.1", "next": "15.3.0", "payload": "3.43.0", "pg": "^8.16.2", "prism-react-renderer": "^2.4.1", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.58.1", "sharp": "0.32.6", "tailwind-merge": "^3.3.1", "ts-node": "^10.9.2"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@types/node": "^22.5.4", "@types/react": "19.1.0", "@types/react-dom": "19.1.2", "autoprefixer": "^10.4.21", "eslint": "^9.16.0", "eslint-config-next": "15.3.0", "postcss": "^8.5.6", "prettier": "^3.4.2", "tailwindcss": "^4.1.10", "typescript": "5.7.3"}, "engines": {"node": "^18.20.2 || >=20.9.0", "pnpm": "^9 || ^10"}, "pnpm": {"onlyBuiltDependencies": ["sharp"]}}