# Lullaby Clinic Authentication Security

This document outlines the comprehensive authentication security system implemented for the Lullaby Clinic application, which enforces strict access controls through Supabase Auth verification.

## 🔐 Security Overview

The authentication system implements a **dual-layer security approach**:

1. **Supabase Auth Verification**: Users must be pre-approved and verified in the Supabase Auth database
2. **Payload CMS Authentication**: Standard Payload CMS authentication with additional security hooks

## 🛡️ Security Features

### ✅ **Registration Restrictions**
- Only users pre-approved in Supabase Auth can register
- Email verification is required in Supabase Auth before registration
- Unauthorized registration attempts are blocked with clear error messages

### ✅ **Login Restrictions**  
- Only verified users in Supabase Auth can login
- Email confirmation status is checked before allowing login
- Failed authorization attempts are logged and blocked

### ✅ **Real-time Verification**
- Every login/registration attempt is verified against Supabase Auth in real-time
- User verification status is synced between systems
- Automatic blocking of unauthorized access attempts

## 🔧 Implementation Details

### **Environment Variables Required**
```bash
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### **API Endpoints**

#### 1. Verification Endpoint
```
POST /api/verify-supabase-auth
```
**Purpose**: Check if a user is authorized for registration or login
**Body**: 
```json
{
  "email": "<EMAIL>",
  "action": "register" | "login"
}
```

#### 2. Secure Registration
```
POST /api/secure-register
```
**Purpose**: Register users with Supabase Auth verification
**Body**:
```json
{
  "email": "<EMAIL>", 
  "password": "securepassword"
}
```

#### 3. Secure Login
```
POST /api/secure-login
```
**Purpose**: Login users with Supabase Auth verification
**Body**:
```json
{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

### **Collection Hooks**

The Users collection includes several security hooks:

- **beforeOperation**: Blocks unauthorized registration attempts
- **beforeLogin**: Verifies user authorization before login
- **afterChange**: Syncs Supabase Auth data with Payload user records

## 🚀 Usage Instructions

### **Adding Authorized Users**

Use the test script to manage authorized users:

```bash
# List all authorized users
node scripts/testSupabaseAuth.js list

# Add a new authorized user
node scripts/testSupabaseAuth.<NAME_EMAIL>

# Test user verification
node scripts/testSupabaseAuth.<NAME_EMAIL>

# Setup default users
node scripts/testSupabaseAuth.js setup
```

### **Managing User Access**

1. **To Grant Access**: Add user to Supabase Auth with confirmed email
2. **To Revoke Access**: Remove user from Supabase Auth or unconfirm email
3. **To Check Status**: Use the verification endpoint or test script

## 🔍 Security Monitoring

### **Logging**
All authentication attempts are logged with detailed information:
- ✅ Successful authorizations
- 🚫 Blocked unauthorized attempts  
- 🔍 Verification checks
- ❌ Error conditions

### **Error Codes**
- `UNAUTHORIZED_REGISTRATION`: User not approved for registration
- `UNAUTHORIZED_LOGIN`: User not approved for login
- `USER_NOT_FOUND`: User not in Supabase Auth database
- `EMAIL_NOT_CONFIRMED`: User email not verified in Supabase Auth
- `INVALID_CREDENTIALS`: Wrong email/password combination
- `ACCOUNT_LOCKED`: Too many failed login attempts

## 🛠️ Administration

### **Admin Panel Access**
The admin login page displays security notices informing users about:
- Authorization requirements
- Verification process
- Contact information for access requests

### **User Management**
Authorized users can be managed through:
1. **Supabase Dashboard**: https://supabase.com/dashboard/project/ueiouusrrngdjrcoctem/auth/users
2. **Test Script**: `scripts/testSupabaseAuth.js`
3. **API Endpoints**: Direct API calls for programmatic management

## 🔒 Security Best Practices

1. **Regular Audits**: Periodically review authorized users list
2. **Access Monitoring**: Monitor authentication logs for suspicious activity
3. **Email Verification**: Ensure all users have confirmed email addresses
4. **Strong Passwords**: Enforce strong password policies
5. **Service Key Security**: Keep Supabase service role key secure and rotate regularly

## 🚨 Troubleshooting

### **Common Issues**

**User can't register/login:**
- Check if user exists in Supabase Auth
- Verify email is confirmed in Supabase Auth
- Check server logs for specific error messages

**"User not found" errors:**
- User needs to be added to Supabase Auth first
- Use the test script to add authorized users

**"Email not confirmed" errors:**
- User email must be verified in Supabase Auth
- Admin can manually confirm emails in Supabase dashboard

### **Testing Commands**
```bash
# Test the authentication system
node scripts/testSupabaseAuth.<NAME_EMAIL>

# Check server logs
tail -f logs/auth.log

# Verify Supabase connection
curl -X POST http://localhost:3000/api/verify-supabase-auth \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","action":"login"}'
```

## 📞 Support

For authentication issues or access requests:
1. Check this documentation first
2. Review server logs for error details
3. Use the test script to verify user status
4. Contact system administrator for user management

---

**Security Notice**: This authentication system provides enterprise-level security by requiring pre-approval of all users. Unauthorized access attempts are automatically blocked and logged.
