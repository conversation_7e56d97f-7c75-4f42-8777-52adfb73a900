import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config()

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY
)

async function checkTables() {
  console.log('🔍 Checking table structures...\n')
  
  console.log('📋 Users table:')
  const { data: users, error: usersError } = await supabase
    .from('users')
    .select('id, email')
    .limit(3)
  
  if (usersError) {
    console.log('❌ Users error:', usersError.message)
  } else {
    console.log('✅ Users sample:', users)
    if (users.length > 0) {
      console.log('   ID type:', typeof users[0].id)
    }
  }
  
  console.log('\n📋 Admin Users table:')
  const { data: adminUsers, error: adminError } = await supabase
    .from('admin_users')
    .select('id, role')
    .limit(3)
  
  if (adminError) {
    console.log('❌ Admin users error:', adminError.message)
  } else {
    console.log('✅ Admin users sample:', adminUsers)
    if (adminUsers.length > 0) {
      console.log('   ID type:', typeof adminUsers[0].id)
    }
  }
  
  console.log('\n📋 Admin Profiles table:')
  const { data: adminProfiles, error: profileError } = await supabase
    .from('admin_profiles')
    .select('id, email')
    .limit(3)
  
  if (profileError) {
    console.log('❌ Admin profiles error:', profileError.message)
  } else {
    console.log('✅ Admin profiles sample:', adminProfiles)
    if (adminProfiles.length > 0) {
      console.log('   ID type:', typeof adminProfiles[0].id)
    }
  }
}

checkTables().catch(console.error)
