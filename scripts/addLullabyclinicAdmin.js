import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'
import crypto from 'crypto'

dotenv.config()

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY
)

async function addLullabyclinicAdmin() {
  console.log('🚀 Adding <EMAIL> as super_admin...\n')
  
  const email = '<EMAIL>'
  console.log('📧 Target email:', email)
  const targetRole = 'super_admin'
  const now = new Date().toISOString()
  
  try {
    let userId
    
    console.log('🔍 Checking if user exists...')
    const { data: existingUser, error: userCheckError } = await supabase
      .from('users')
      .select('id')
      .eq('email', email)
      .single()
    
    if (userCheckError && userCheckError.code !== 'PGRST116') {
      throw userCheckError
    }
    
    if (existingUser) {
      userId = existingUser.id
      console.log(`✅ User found with ID: ${userId}`)
    } else {
      console.log('👤 User not found, creating new user...')
      
      const { data: newUser, error: createUserError } = await supabase
        .from('users')
        .insert({
          email: email,
          created_at: now,
          updated_at: now
        })
        .select('id')
        .single()
      
      if (createUserError) {
        throw createUserError
      }
      
      userId = newUser.id
      console.log(`✅ User created with ID: ${userId}`)
    }
    
    console.log('📝 Creating/updating admin profile...')
    
    // Try with integer ID first (matching users table)
    console.log(`🔑 Using user ID: ${userId}`)
    
    const { error: profileError } = await supabase
      .from('admin_profiles')
      .upsert({
        id: userId,
        first_name: 'Lullaby',
        last_name: 'Clinic',
        email: email,
        bio: 'Main administrative account for Lullaby Clinic',
        preferences: {},
        created_at: now,
        updated_at: now,
      }, { 
        onConflict: 'id',
        ignoreDuplicates: false 
      })
    
    if (profileError) {
      throw profileError
    }
    console.log('✅ Admin profile created/updated')
    
    console.log('🔐 Creating/updating admin user permissions...')
    const { error: adminError } = await supabase
      .from('admin_users')
      .upsert({
        id: userId,
        role: targetRole,
        permissions: {
          all_collections: true,
          manage_users: true,
          manage_settings: true,
          manage_content: true,
          view_analytics: true
        },
        department: 'Administration',
        employee_id: 'ADMIN001',
        hire_date: new Date().toISOString().split('T')[0],
        is_active: true,
        created_at: now,
        updated_at: now,
      }, { 
        onConflict: 'id',
        ignoreDuplicates: false 
      })
    
    if (adminError) {
      throw adminError
    }
    console.log('✅ Admin user permissions created/updated')
    
    console.log('\n🎉 SUCCESS!')
    console.log(`📧 Email: ${email}`)
    console.log(`👤 User ID: ${userId}`)
    console.log(`🆔 Admin UUID: ${adminUuid}`)
    console.log(`🔑 Role: ${targetRole}`)
    console.log(`✨ Status: Active admin user ready to use`)
    
    console.log('\n📋 Next steps:')
    console.log('1. The user can now log in with their email')
    console.log('2. They will have full super_admin privileges')
    console.log('3. Check the admin panel to verify the user appears correctly')
    
  } catch (error) {
    console.error('\n❌ ERROR:', error.message || error)
    console.error('\n🔧 Troubleshooting:')
    console.error('1. Ensure SUPABASE_SERVICE_ROLE_KEY is set in your .env file')
    console.error('2. Check that the admin_users and admin_profiles tables exist')
    console.error('3. Verify your Supabase connection settings')
    process.exit(1)
  }
}

addLullabyclinicAdmin()
