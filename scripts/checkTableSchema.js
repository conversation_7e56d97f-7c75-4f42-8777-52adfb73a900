import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config()

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY
)

async function checkTableSchema() {
  console.log('🔍 Checking actual table schemas...\n')
  
  const tables = ['users', 'admin_users', 'admin_profiles']
  
  for (const tableName of tables) {
    console.log(`📋 ${tableName} table schema:`)
    
    const { data, error } = await supabase.rpc('get_table_schema', {
      table_name: tableName
    })
    
    if (error) {
      // Fallback: try to get column info using information_schema
      const { data: columns, error: colError } = await supabase
        .rpc('exec_sql', {
          sql: `SELECT column_name, data_type, is_nullable FROM information_schema.columns WHERE table_name = '${tableName}' AND table_schema = 'public'`
        })
      
      if (colError) {
        console.log('❌ Error:', colError.message)
      } else {
        console.log('✅ Columns:', columns)
      }
    } else {
      console.log('✅ Schema:', data)
    }
    console.log('')
  }
}

checkTableSchema().catch(console.error)
