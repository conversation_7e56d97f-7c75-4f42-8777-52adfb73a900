#!/usr/bin/env node

/**
 * Test script for Forgot Password functionality
 * This script tests the forgot password and reset password flow
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

const supabaseUrl = process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Required: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

// Create Supabase admin client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function testForgotPasswordEndpoint(email) {
  console.log(`\n🔍 Testing forgot password endpoint for: ${email}\n`)
  
  try {
    const response = await fetch('http://localhost:3000/api/forgot-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email })
    })

    const result = await response.json()
    
    console.log(`📊 Response Status: ${response.status}`)
    console.log(`📋 Response Body:`, result)
    
    if (response.ok && result.success) {
      console.log('✅ Forgot password request successful')
      return true
    } else {
      console.log('❌ Forgot password request failed:', result.error)
      return false
    }

  } catch (error) {
    console.error('❌ Error testing forgot password endpoint:', error.message)
    return false
  }
}

async function testResetPasswordEndpoint(token, newPassword) {
  console.log(`\n🔍 Testing reset password endpoint with token: ${token.substring(0, 8)}...\n`)
  
  try {
    const response = await fetch('http://localhost:3000/api/reset-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ 
        token,
        password: newPassword
      })
    })

    const result = await response.json()
    
    console.log(`📊 Response Status: ${response.status}`)
    console.log(`📋 Response Body:`, result)
    
    if (response.ok && result.success) {
      console.log('✅ Password reset successful')
      return true
    } else {
      console.log('❌ Password reset failed:', result.error)
      return false
    }

  } catch (error) {
    console.error('❌ Error testing reset password endpoint:', error.message)
    return false
  }
}

async function checkPayloadUser(email) {
  console.log(`\n🔍 Checking user in Payload CMS: ${email}\n`)
  
  try {
    const response = await fetch('http://localhost:3000/api/users', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    })

    if (!response.ok) {
      console.log('❌ Failed to fetch users from Payload CMS')
      return null
    }

    const result = await response.json()
    const user = result.docs?.find(u => u.email === email)
    
    if (user) {
      console.log('✅ User found in Payload CMS')
      console.log(`   ID: ${user.id}`)
      console.log(`   Email: ${user.email}`)
      console.log(`   Supabase Verified: ${user.isSupabaseVerified}`)
      console.log(`   Reset Token: ${user.resetPasswordToken ? 'Present' : 'None'}`)
      console.log(`   Reset Expiration: ${user.resetPasswordExpiration || 'None'}`)
      return user
    } else {
      console.log('❌ User not found in Payload CMS')
      return null
    }

  } catch (error) {
    console.error('❌ Error checking Payload user:', error.message)
    return null
  }
}

async function testEmailConfiguration() {
  console.log('\n🔍 Testing email configuration...\n')
  
  try {
    // Test if the server is running and email is configured
    const response = await fetch('http://localhost:3000/api/users/me', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    })

    if (response.status === 401) {
      console.log('✅ Server is running (got expected 401 for unauthenticated request)')
      return true
    } else {
      console.log(`📊 Server response: ${response.status}`)
      return true
    }

  } catch (error) {
    console.error('❌ Server not running or not accessible:', error.message)
    return false
  }
}

async function main() {
  console.log('🔐 Lullaby Clinic - Forgot Password Test\n')
  console.log('=' .repeat(60))

  const args = process.argv.slice(2)
  const command = args[0]
  const email = args[1] || '<EMAIL>'

  switch (command) {
    case 'forgot':
      await testEmailConfiguration()
      await testForgotPasswordEndpoint(email)
      await checkPayloadUser(email)
      break
      
    case 'reset':
      const token = args[2]
      const password = args[3] || 'newpassword123'
      
      if (!token) {
        console.error('❌ Reset token is required')
        console.log('Usage: node testForgotPassword.js reset <email> <token> [password]')
        process.exit(1)
      }
      
      await testResetPasswordEndpoint(token, password)
      break
      
    case 'check':
      await checkPayloadUser(email)
      break
      
    case 'server':
      await testEmailConfiguration()
      break
      
    case 'all':
      await testEmailConfiguration()
      await testForgotPasswordEndpoint(email)
      await checkPayloadUser(email)
      break
      
    default:
      console.log('📖 Available commands:')
      console.log('  forgot <email>           - Test forgot password endpoint')
      console.log('  reset <email> <token>    - Test reset password endpoint')
      console.log('  check <email>            - Check user in Payload CMS')
      console.log('  server                   - Test server connectivity')
      console.log('  all <email>              - Run all tests')
      console.log('\nExamples:')
      console.log('  node testForgotPassword.<NAME_EMAIL>')
      console.log('  node testForgotPassword.<NAME_EMAIL> abc123token')
      console.log('  node testForgotPassword.<NAME_EMAIL>')
      console.log('  node testForgotPassword.<NAME_EMAIL>')
  }

  console.log('\n' + '=' .repeat(60))
}

main().catch(console.error)
