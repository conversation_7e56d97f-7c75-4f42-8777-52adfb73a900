import { Pool } from 'pg'
import dotenv from 'dotenv'

dotenv.config()

async function checkBlogPostsSchema() {
  const pool = new Pool({
    connectionString: process.env.DATABASE_URI,
  })

  try {
    console.log('🔍 Checking blog_posts table schema...\n')

    const result = await pool.query(`
      SELECT 
        column_name,
        data_type,
        is_nullable,
        column_default,
        character_maximum_length
      FROM information_schema.columns 
      WHERE table_name = 'blog_posts' 
      ORDER BY ordinal_position;
    `)

    console.log('📋 Blog Posts Table Schema:')
    console.log('='.repeat(80))
    result.rows.forEach((row) => {
      console.log(`Column: ${row.column_name}`)
      console.log(`  Type: ${row.data_type}`)
      console.log(`  Nullable: ${row.is_nullable}`)
      console.log(`  Default: ${row.column_default || 'None'}`)
      console.log(`  Max Length: ${row.character_maximum_length || 'N/A'}`)
      console.log('')
    })

    // Check if table exists
    const tableExists = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'blog_posts'
      );
    `)

    console.log(`✅ Table exists: ${tableExists.rows[0].exists}`)
  } catch (error) {
    console.error('❌ Error checking schema:', error.message)
  } finally {
    await pool.end()
  }
}

checkBlogPostsSchema()
