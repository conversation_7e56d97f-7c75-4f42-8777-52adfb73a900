#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to add Supabase Auth fields to the users table
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

const supabaseUrl = process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Required: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

// Create Supabase admin client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function addSupabaseAuthFields() {
  console.log('🔧 Adding Supabase Auth fields to users table...\n')
  
  try {
    // Add the new columns
    const { error: alterError } = await supabase.rpc('exec_sql', {
      sql: `
        -- Add Supabase Auth fields to users table
        ALTER TABLE users 
        ADD COLUMN IF NOT EXISTS supabase_auth_id varchar,
        ADD COLUMN IF NOT EXISTS is_supabase_verified boolean DEFAULT false;
        
        -- Create indexes for better performance
        CREATE INDEX IF NOT EXISTS idx_users_supabase_auth_id ON users (supabase_auth_id);
        CREATE INDEX IF NOT EXISTS idx_users_is_supabase_verified ON users (is_supabase_verified);
      `
    })

    if (alterError) {
      console.error('❌ Error adding fields:', alterError)
      return false
    }

    console.log('✅ Successfully added Supabase Auth fields to users table')
    
    // Verify the columns were added
    const { data: columns, error: columnsError } = await supabase.rpc('exec_sql', {
      sql: `
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name IN ('supabase_auth_id', 'is_supabase_verified')
        ORDER BY column_name;
      `
    })

    if (columnsError) {
      console.error('❌ Error verifying columns:', columnsError)
      return false
    }

    if (columns && columns.length > 0) {
      console.log('\n📋 Verified new columns:')
      columns.forEach(col => {
        console.log(`  - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable}, default: ${col.column_default || 'none'})`)
      })
    }

    return true

  } catch (error) {
    console.error('❌ Error adding Supabase Auth fields:', error)
    return false
  }
}

async function main() {
  console.log('🔐 Lullaby Clinic - Add Supabase Auth Fields\n')
  console.log('=' .repeat(50))

  const success = await addSupabaseAuthFields()
  
  if (success) {
    console.log('\n✅ Migration completed successfully!')
    console.log('The users table now includes:')
    console.log('  - supabase_auth_id: varchar (stores Supabase user ID)')
    console.log('  - is_supabase_verified: boolean (tracks verification status)')
  } else {
    console.log('\n❌ Migration failed!')
    process.exit(1)
  }

  console.log('\n' + '=' .repeat(50))
}

main().catch(console.error)
