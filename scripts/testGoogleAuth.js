#!/usr/bin/env node

/**
 * Test script for Google OAuth Integration
 * This script tests the Google authentication flow and endpoints
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

const supabaseUrl = process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Required: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

// Create Supabase admin client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function testGoogleOAuthConfig() {
  console.log('\n🔍 Testing Google OAuth Configuration...\n')
  
  try {
    // Test the auth configuration endpoint
    const response = await fetch('http://localhost:3000/api/verify-supabase-auth', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        action: 'login'
      })
    })

    const result = await response.json()
    
    if (response.ok && result.allowed) {
      console.log('✅ Supabase Auth verification endpoint working')
      console.log(`   User: ${result.supabaseUser?.email}`)
      console.log(`   Confirmed: ${result.supabaseUser?.email_confirmed_at ? 'Yes' : 'No'}`)
    } else {
      console.log('❌ Supabase Auth verification failed:', result.error)
    }

  } catch (error) {
    console.error('❌ Error testing auth configuration:', error.message)
  }
}

async function testGoogleOAuthSync() {
  console.log('\n🔍 Testing Google OAuth Sync Endpoint...\n')
  
  try {
    // Test the sync endpoint with a mock Google user
    const response = await fetch('http://localhost:3000/api/google-oauth-sync', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        supabaseUserId: 'test-uuid-123',
        userData: {
          name: 'Test User',
          provider: 'google'
        }
      })
    })

    const result = await response.json()
    
    if (response.ok && result.success) {
      console.log('✅ Google OAuth sync endpoint working')
      console.log(`   User synced: ${result.user?.email}`)
      console.log(`   Payload ID: ${result.user?.id}`)
      console.log(`   Verified: ${result.user?.isSupabaseVerified}`)
    } else {
      console.log('❌ Google OAuth sync failed:', result.error)
    }

  } catch (error) {
    console.error('❌ Error testing OAuth sync:', error.message)
  }
}

async function checkGoogleOAuthSettings() {
  console.log('\n🔍 Checking Google OAuth Settings in Supabase...\n')
  
  try {
    const { data: { users }, error } = await supabase.auth.admin.listUsers({
      page: 1,
      perPage: 5
    })

    if (error) {
      console.error('❌ Error fetching users:', error)
      return
    }

    console.log(`📋 Found ${users.length} authorized user(s) in Supabase Auth`)
    
    // Check if any users have Google as provider
    const googleUsers = users.filter(user => 
      user.app_metadata?.providers?.includes('google') || 
      user.identities?.some(identity => identity.provider === 'google')
    )

    if (googleUsers.length > 0) {
      console.log(`✅ ${googleUsers.length} user(s) have Google OAuth configured`)
      googleUsers.forEach(user => {
        console.log(`   - ${user.email} (Google OAuth enabled)`)
      })
    } else {
      console.log('ℹ️  No users with Google OAuth found (this is normal for new setup)')
    }

  } catch (error) {
    console.error('❌ Error checking OAuth settings:', error)
  }
}

async function testGoogleOAuthURL() {
  console.log('\n🔍 Testing Google OAuth URL Generation...\n')
  
  try {
    // Create a client-side Supabase client to test OAuth URL generation
    const clientSupabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_ANON_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const { data, error } = await clientSupabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: 'http://localhost:3000/admin/auth/callback',
        queryParams: {
          access_type: 'offline',
          prompt: 'consent'
        }
      }
    })

    if (error) {
      console.error('❌ Error generating Google OAuth URL:', error)
    } else {
      console.log('✅ Google OAuth URL generation successful')
      console.log(`   Provider: ${data.provider}`)
      console.log(`   URL: ${data.url ? 'Generated successfully' : 'Not generated'}`)
    }

  } catch (error) {
    console.error('❌ Error testing OAuth URL:', error)
  }
}

async function main() {
  console.log('🔐 Lullaby Clinic - Google OAuth Integration Test\n')
  console.log('=' .repeat(60))

  const args = process.argv.slice(2)
  const command = args[0]

  switch (command) {
    case 'config':
      await testGoogleOAuthConfig()
      break
      
    case 'sync':
      await testGoogleOAuthSync()
      break
      
    case 'settings':
      await checkGoogleOAuthSettings()
      break
      
    case 'url':
      await testGoogleOAuthURL()
      break
      
    case 'all':
      await testGoogleOAuthConfig()
      await testGoogleOAuthSync()
      await checkGoogleOAuthSettings()
      await testGoogleOAuthURL()
      break
      
    default:
      console.log('📖 Available commands:')
      console.log('  config    - Test Supabase Auth verification endpoint')
      console.log('  sync      - Test Google OAuth sync endpoint')
      console.log('  settings  - Check Google OAuth settings in Supabase')
      console.log('  url       - Test Google OAuth URL generation')
      console.log('  all       - Run all tests')
      console.log('\nExamples:')
      console.log('  node testGoogleAuth.js config')
      console.log('  node testGoogleAuth.js all')
  }

  console.log('\n' + '=' .repeat(60))
}

main().catch(console.error)
