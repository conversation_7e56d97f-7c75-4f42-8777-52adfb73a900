#!/usr/bin/env node

/**
 * Test script for Supabase Authentication Integration
 * This script tests the authentication restrictions and verifies users
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

const supabaseUrl = process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Required: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

// Create Supabase admin client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
})

async function listAuthorizedUsers() {
  console.log('\n🔍 Listing all authorized users in Supabase Auth...\n')

  try {
    const {
      data: { users },
      error,
    } = await supabase.auth.admin.listUsers({
      page: 1,
      perPage: 1000,
    })

    if (error) {
      console.error('❌ Error fetching users:', error)
      return
    }

    if (users.length === 0) {
      console.log('📭 No users found in Supabase Auth database')
      return
    }

    console.log(`📋 Found ${users.length} authorized user(s):\n`)

    users.forEach((user, index) => {
      console.log(`${index + 1}. Email: ${user.email}`)
      console.log(`   ID: ${user.id}`)
      console.log(`   Confirmed: ${user.email_confirmed_at ? '✅ Yes' : '❌ No'}`)
      console.log(`   Created: ${new Date(user.created_at).toLocaleString()}`)
      console.log(
        `   Last Sign In: ${user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'Never'}`,
      )
      console.log('')
    })
  } catch (error) {
    console.error('❌ Error listing users:', error)
  }
}

async function addAuthorizedUser(email, password = 'TempPassword123!') {
  console.log(`\n➕ Adding authorized user: ${email}`)

  try {
    const { data, error } = await supabase.auth.admin.createUser({
      email: email,
      password: password,
      email_confirm: true, // Auto-confirm email
    })

    if (error) {
      console.error('❌ Error creating user:', error.message)
      return false
    }

    console.log(`✅ User created successfully: ${email}`)
    console.log(`   ID: ${data.user.id}`)
    console.log(`   Email Confirmed: ${data.user.email_confirmed_at ? 'Yes' : 'No'}`)
    return true
  } catch (error) {
    console.error('❌ Error adding user:', error)
    return false
  }
}

async function testUserVerification(email) {
  console.log(`\n🔍 Testing verification for: ${email}`)

  try {
    // Test the verification endpoint
    const response = await fetch('http://localhost:3001/api/verify-supabase-auth', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: email,
        action: 'login',
      }),
    })

    const result = await response.json()

    if (response.ok && result.allowed) {
      console.log(`✅ User ${email} is authorized for login`)
      if (result.supabaseUser) {
        console.log(`   Supabase ID: ${result.supabaseUser.id}`)
        console.log(`   Email Confirmed: ${result.supabaseUser.email_confirmed_at ? 'Yes' : 'No'}`)
      }
    } else {
      console.log(`❌ User ${email} is NOT authorized: ${result.error}`)
    }
  } catch (error) {
    console.error('❌ Error testing verification:', error.message)
  }
}

async function main() {
  console.log('🔐 Lullaby Clinic - Supabase Authentication Test\n')
  console.log('='.repeat(50))

  const args = process.argv.slice(2)
  const command = args[0]

  switch (command) {
    case 'list':
      await listAuthorizedUsers()
      break

    case 'add':
      const email = args[1]
      const password = args[2]
      if (!email) {
        console.log('❌ Usage: node testSupabaseAuth.js add <email> [password]')
        break
      }
      await addAuthorizedUser(email, password)
      break

    case 'test':
      const testEmail = args[1]
      if (!testEmail) {
        console.log('❌ Usage: node testSupabaseAuth.js test <email>')
        break
      }
      await testUserVerification(testEmail)
      break

    case 'setup':
      console.log('🚀 Setting up default authorized users...\n')

      // Add lullabyclinic admin user
      await addAuthorizedUser('<EMAIL>')

      // Add any other default users here
      // await addAuthorizedUser('<EMAIL>')

      console.log('\n📋 Current authorized users:')
      await listAuthorizedUsers()
      break

    default:
      console.log('📖 Available commands:')
      console.log('  list                     - List all authorized users')
      console.log('  add <email> [password]   - Add a new authorized user')
      console.log('  test <email>             - Test user verification')
      console.log('  setup                    - Setup default authorized users')
      console.log('\nExamples:')
      console.log('  node testSupabaseAuth.js list')
      console.log('  node testSupabaseAuth.<NAME_EMAIL>')
      console.log('  node testSupabaseAuth.<NAME_EMAIL>')
      console.log('  node testSupabaseAuth.js setup')
  }

  console.log('\n' + '='.repeat(50))
}

main().catch(console.error)
