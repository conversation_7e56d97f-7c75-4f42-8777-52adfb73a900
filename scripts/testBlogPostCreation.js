import dotenv from 'dotenv'

dotenv.config()

async function testBlogPostCreation() {
  const baseUrl = process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3000'

  console.log('🧪 Testing Blog Post Creation...\n')

  try {
    // Test data for blog post creation
    const testBlogPost = {
      title: 'Test Blog Post - Skin Care Tips',
      slug: 'test-blog-post-skin-care-tips',
      content:
        'This is a test blog post about skin care tips. It contains valuable information for our clinic patients.',
      excerpt: 'A comprehensive guide to daily skin care routines.',
      author_id: 'test-author-123',
      category: 'Skin Care',
      tags: 'skincare, beauty, health',
      is_published: true,
      is_featured: false,
      language: 'th',
      seo_title: 'Best Skin Care Tips - Lullaby Clinic',
      seo_description:
        'Learn the best skin care tips from our expert dermatologists at Lullaby Clinic.',
    }

    console.log('📝 Attempting to create blog post...')
    console.log('Title:', testBlogPost.title)
    console.log('Slug:', testBlogPost.slug)
    console.log('')

    // Make API request to create blog post
    const response = await fetch(`${baseUrl}/api/blog-posts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testBlogPost),
    })

    console.log('📊 Response Status:', response.status)
    console.log('📊 Response Status Text:', response.statusText)

    const responseData = await response.text()

    if (response.ok) {
      console.log('✅ Blog post created successfully!')
      try {
        const jsonData = JSON.parse(responseData)
        console.log('📄 Created blog post ID:', jsonData.id)
        console.log('📄 Created blog post title:', jsonData.title)
      } catch (e) {
        console.log('📄 Response data:', responseData)
      }
    } else {
      console.log('❌ Failed to create blog post')
      console.log('📄 Error response:', responseData)

      try {
        const errorData = JSON.parse(responseData)
        if (errorData.errors) {
          console.log('🔍 Validation errors:')
          errorData.errors.forEach((error) => {
            console.log(`  - ${error.field}: ${error.message}`)
          })
        }
      } catch (e) {
        // Response is not JSON
      }
    }

    // Test fetching blog posts
    console.log('\n📋 Testing blog posts list...')
    const listResponse = await fetch(`${baseUrl}/api/blog-posts`)
    console.log('📊 List Response Status:', listResponse.status)

    if (listResponse.ok) {
      const listData = await listResponse.json()
      console.log('✅ Blog posts list retrieved successfully!')
      console.log('📊 Total blog posts:', listData.totalDocs || listData.docs?.length || 'Unknown')
    }
  } catch (error) {
    console.error('❌ Error testing blog post creation:', error.message)
  }
}

testBlogPostCreation()
