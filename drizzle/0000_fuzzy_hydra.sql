-- Current sql file was generated after introspecting the database
-- If you want to run this migration please uncomment this code before executing migrations
/*
CREATE TYPE "public"."admin_role" AS ENUM('super_admin', 'admin', 'manager', 'staff', 'content_editor', 'analyst');--> statement-breakpoint
CREATE TYPE "public"."appointment_status" AS ENUM('scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show');--> statement-breakpoint
CREATE TYPE "public"."campaign_status" AS ENUM('draft', 'scheduled', 'active', 'paused', 'completed', 'cancelled');--> statement-breakpoint
CREATE TYPE "public"."interaction_priority" AS ENUM('low', 'medium', 'high', 'urgent');--> statement-breakpoint
CREATE TYPE "public"."notification_type" AS ENUM('appointment_confirmation', 'appointment_reminder', 'payment_confirmation', 'treatment_followup', 'marketing');--> statement-breakpoint
CREATE TYPE "public"."payment_method" AS ENUM('cash', 'card', 'bank_transfer', 'insurance');--> statement-breakpoint
CREATE TYPE "public"."payment_status" AS ENUM('pending', 'paid', 'failed', 'refunded', 'partial');--> statement-breakpoint
CREATE TYPE "public"."treatment_difficulty" AS ENUM('beginner', 'intermediate', 'advanced');--> statement-breakpoint
CREATE TYPE "public"."user_role" AS ENUM('patient', 'doctor', 'admin', 'staff');--> statement-breakpoint
CREATE TABLE "doctors" (
	"id" uuid PRIMARY KEY NOT NULL,
	"license_number" varchar(100) NOT NULL,
	"specialization" varchar(200) NOT NULL,
	"qualification" text NOT NULL,
	"experience_years" integer DEFAULT 0,
	"bio" text,
	"consultation_fee" numeric(10, 2),
	"languages_spoken" text[],
	"working_hours" jsonb,
	"is_available" boolean DEFAULT true,
	"rating" numeric(3, 2) DEFAULT '5.00',
	"total_reviews" integer DEFAULT 0,
	"image_url" text,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "doctors_license_number_key" UNIQUE("license_number")
);
--> statement-breakpoint
ALTER TABLE "doctors" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "service_categories" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
	"name" varchar(200) NOT NULL,
	"description" text,
	"slug" varchar(200) NOT NULL,
	"image_url" text,
	"sort_order" integer DEFAULT 0,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "service_categories_slug_key" UNIQUE("slug")
);
--> statement-breakpoint
ALTER TABLE "service_categories" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "services" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
	"category_id" uuid,
	"name" varchar(200) NOT NULL,
	"description" text NOT NULL,
	"short_description" varchar(500),
	"slug" varchar(200) NOT NULL,
	"base_price" numeric(10, 2) NOT NULL,
	"discounted_price" numeric(10, 2),
	"duration_minutes" integer DEFAULT 60 NOT NULL,
	"difficulty" "treatment_difficulty" DEFAULT 'beginner',
	"preparation_instructions" text,
	"aftercare_instructions" text,
	"contraindications" text,
	"benefits" text[],
	"procedures" text[],
	"image_url" text,
	"gallery_images" text[],
	"is_popular" boolean DEFAULT false,
	"is_featured" boolean DEFAULT false,
	"sort_order" integer DEFAULT 0,
	"is_active" boolean DEFAULT true,
	"seo_title" varchar(200),
	"seo_description" varchar(300),
	"seo_keywords" text[],
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "services_slug_key" UNIQUE("slug")
);
--> statement-breakpoint
ALTER TABLE "services" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "appointment_slots" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
	"doctor_id" uuid,
	"start_time" timestamp with time zone NOT NULL,
	"end_time" timestamp with time zone NOT NULL,
	"is_available" boolean DEFAULT true,
	"recurring_rule" jsonb,
	"created_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "appointment_slots" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "appointments" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
	"patient_id" uuid,
	"doctor_id" uuid,
	"service_id" uuid,
	"slot_id" uuid,
	"appointment_date" timestamp with time zone NOT NULL,
	"duration_minutes" integer DEFAULT 60 NOT NULL,
	"status" "appointment_status" DEFAULT 'scheduled',
	"total_amount" numeric(10, 2) NOT NULL,
	"deposit_amount" numeric(10, 2) DEFAULT '0',
	"patient_notes" text,
	"doctor_notes" text,
	"treatment_plan" text,
	"before_photos" text[],
	"after_photos" text[],
	"prescription" text,
	"next_appointment_recommended" boolean DEFAULT false,
	"followup_date" date,
	"confirmation_sent_at" timestamp with time zone,
	"reminder_sent_at" timestamp with time zone,
	"checked_in_at" timestamp with time zone,
	"completed_at" timestamp with time zone,
	"cancelled_at" timestamp with time zone,
	"cancellation_reason" text,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"appointment_type_id" uuid,
	"patient_phone" text,
	"patient_email" text
);
--> statement-breakpoint
ALTER TABLE "appointments" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "invoices" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
	"appointment_id" uuid,
	"payment_id" uuid,
	"invoice_number" varchar(100) NOT NULL,
	"issue_date" date DEFAULT CURRENT_DATE NOT NULL,
	"due_date" date,
	"subtotal" numeric(10, 2) NOT NULL,
	"tax_amount" numeric(10, 2) DEFAULT '0',
	"discount_amount" numeric(10, 2) DEFAULT '0',
	"total_amount" numeric(10, 2) NOT NULL,
	"currency" varchar(3) DEFAULT 'THB',
	"notes" text,
	"is_paid" boolean DEFAULT false,
	"pdf_url" text,
	"created_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "invoices_invoice_number_key" UNIQUE("invoice_number")
);
--> statement-breakpoint
ALTER TABLE "invoices" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "payments" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
	"appointment_id" uuid,
	"patient_id" uuid,
	"amount" numeric(10, 2) NOT NULL,
	"payment_method" "payment_method" NOT NULL,
	"payment_status" "payment_status" DEFAULT 'pending',
	"transaction_id" varchar(200),
	"stripe_payment_intent_id" varchar(200),
	"payment_date" timestamp with time zone,
	"refund_amount" numeric(10, 2) DEFAULT '0',
	"refund_date" timestamp with time zone,
	"refund_reason" text,
	"receipt_url" text,
	"metadata" jsonb,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "payments_transaction_id_key" UNIQUE("transaction_id")
);
--> statement-breakpoint
ALTER TABLE "payments" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "clinic_settings" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
	"key" varchar(100) NOT NULL,
	"value" text,
	"description" text,
	"data_type" varchar(20) DEFAULT 'string',
	"is_public" boolean DEFAULT false,
	"updated_at" timestamp with time zone DEFAULT now(),
	"updated_by" uuid,
	CONSTRAINT "clinic_settings_key_key" UNIQUE("key")
);
--> statement-breakpoint
ALTER TABLE "clinic_settings" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "promotions" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
	"title" varchar(200) NOT NULL,
	"description" text NOT NULL,
	"discount_type" varchar(20) NOT NULL,
	"discount_value" numeric(10, 2) NOT NULL,
	"min_purchase_amount" numeric(10, 2),
	"max_discount_amount" numeric(10, 2),
	"promo_code" varchar(50),
	"start_date" timestamp with time zone NOT NULL,
	"end_date" timestamp with time zone NOT NULL,
	"usage_limit" integer,
	"usage_count" integer DEFAULT 0,
	"applicable_services" uuid[],
	"is_active" boolean DEFAULT true,
	"is_featured" boolean DEFAULT false,
	"image_url" text,
	"terms_conditions" text,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "promotions_promo_code_key" UNIQUE("promo_code"),
	CONSTRAINT "promotions_discount_type_check" CHECK ((discount_type)::text = ANY ((ARRAY['percentage'::character varying, 'fixed_amount'::character varying])::text[]))
);
--> statement-breakpoint
ALTER TABLE "promotions" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "newsletter_subscribers" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
	"email" varchar(255) NOT NULL,
	"first_name" varchar(100),
	"last_name" varchar(100),
	"language" varchar(10) DEFAULT 'th',
	"subscribed_at" timestamp with time zone DEFAULT now(),
	"unsubscribed_at" timestamp with time zone,
	"is_active" boolean DEFAULT true,
	"subscriber_tags" text[],
	"source" varchar(100),
	"preferences" jsonb,
	CONSTRAINT "newsletter_subscribers_email_key" UNIQUE("email")
);
--> statement-breakpoint
ALTER TABLE "newsletter_subscribers" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "reviews" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
	"patient_id" uuid,
	"doctor_id" uuid,
	"service_id" uuid,
	"appointment_id" uuid,
	"rating" integer NOT NULL,
	"title" varchar(200),
	"comment" text,
	"is_anonymous" boolean DEFAULT false,
	"is_approved" boolean DEFAULT false,
	"is_featured" boolean DEFAULT false,
	"helpful_count" integer DEFAULT 0,
	"before_photo_url" text,
	"after_photo_url" text,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "reviews_patient_id_appointment_id_key" UNIQUE("patient_id","appointment_id"),
	CONSTRAINT "reviews_rating_check" CHECK ((rating >= 1) AND (rating <= 5))
);
--> statement-breakpoint
ALTER TABLE "reviews" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "blog_posts" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
	"author_id" uuid,
	"title" varchar(300) NOT NULL,
	"slug" varchar(300) NOT NULL,
	"excerpt" text,
	"content" text NOT NULL,
	"featured_image" text,
	"gallery_images" text[],
	"category" varchar(100),
	"tags" text[],
	"difficulty" "treatment_difficulty" DEFAULT 'beginner',
	"read_time_minutes" integer DEFAULT 5,
	"view_count" integer DEFAULT 0,
	"like_count" integer DEFAULT 0,
	"is_published" boolean DEFAULT false,
	"is_featured" boolean DEFAULT false,
	"published_at" timestamp with time zone,
	"seo_title" varchar(200),
	"seo_description" varchar(300),
	"seo_keywords" text[],
	"language" varchar(10) DEFAULT 'th',
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "blog_posts_slug_key" UNIQUE("slug")
);
--> statement-breakpoint
ALTER TABLE "blog_posts" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "notifications" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
	"recipient_id" uuid,
	"type" "notification_type" NOT NULL,
	"channel" varchar(20) NOT NULL,
	"subject" varchar(200),
	"content" text NOT NULL,
	"template_id" varchar(100),
	"appointment_id" uuid,
	"sent_at" timestamp with time zone,
	"delivery_status" varchar(20) DEFAULT 'pending',
	"error_message" text,
	"metadata" jsonb,
	"created_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "notifications_channel_check" CHECK ((channel)::text = ANY ((ARRAY['email'::character varying, 'sms'::character varying, 'push'::character varying, 'in_app'::character varying])::text[]))
);
--> statement-breakpoint
ALTER TABLE "notifications" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "promotion_usage" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
	"promotion_id" uuid,
	"patient_id" uuid,
	"appointment_id" uuid,
	"discount_amount" numeric(10, 2) NOT NULL,
	"used_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "promotion_usage" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "customer_segments" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"criteria" jsonb NOT NULL,
	"color" text DEFAULT '#3B82F6',
	"icon" text,
	"is_dynamic" boolean DEFAULT true,
	"patient_count" integer DEFAULT 0,
	"last_updated_at" timestamp with time zone DEFAULT now(),
	"created_by" uuid,
	"created_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "customer_segments" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "admin_profiles" (
	"id" uuid PRIMARY KEY NOT NULL,
	"first_name" text NOT NULL,
	"last_name" text NOT NULL,
	"email" text NOT NULL,
	"phone" text,
	"avatar_url" text,
	"bio" text,
	"preferences" jsonb DEFAULT '{}'::jsonb,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "admin_profiles_email_key" UNIQUE("email")
);
--> statement-breakpoint
ALTER TABLE "admin_profiles" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "user_profiles" (
	"id" uuid PRIMARY KEY NOT NULL,
	"role" "user_role" DEFAULT 'patient' NOT NULL,
	"first_name" varchar(100) NOT NULL,
	"last_name" varchar(100) NOT NULL,
	"email" varchar(255) NOT NULL,
	"phone" varchar(20),
	"date_of_birth" date,
	"gender" varchar(20),
	"address" text,
	"city" varchar(100),
	"country" varchar(100) DEFAULT 'Thailand',
	"emergency_contact_name" varchar(200),
	"emergency_contact_phone" varchar(20),
	"medical_history" text,
	"allergies" text,
	"current_medications" text,
	"preferred_language" varchar(10) DEFAULT 'th',
	"marketing_consent" boolean DEFAULT false,
	"privacy_consent" boolean DEFAULT true NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"last_login" timestamp with time zone,
	"is_active" boolean DEFAULT true,
	CONSTRAINT "user_profiles_email_key" UNIQUE("email")
);
--> statement-breakpoint
ALTER TABLE "user_profiles" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "customer_notes" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"patient_id" uuid,
	"note_type" text DEFAULT 'general',
	"title" text,
	"content" text NOT NULL,
	"is_private" boolean DEFAULT false,
	"is_important" boolean DEFAULT false,
	"tags" text[],
	"created_by" uuid,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "customer_notes" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "admin_users" (
	"id" uuid PRIMARY KEY NOT NULL,
	"role" "admin_role" DEFAULT 'staff' NOT NULL,
	"permissions" jsonb DEFAULT '{}'::jsonb,
	"department" text,
	"employee_id" text,
	"hire_date" date,
	"is_active" boolean DEFAULT true,
	"last_login_at" timestamp with time zone,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "admin_users_employee_id_key" UNIQUE("employee_id")
);
--> statement-breakpoint
ALTER TABLE "admin_users" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "analytics_events" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"event_type" text NOT NULL,
	"event_category" text NOT NULL,
	"event_data" jsonb NOT NULL,
	"patient_id" uuid,
	"session_id" text,
	"page_url" text,
	"referrer_url" text,
	"ip_address" "inet",
	"user_agent" text,
	"device_type" text,
	"browser" text,
	"os" text,
	"country" text,
	"city" text,
	"created_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "analytics_events" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "customer_interactions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"patient_id" uuid,
	"interaction_type" text NOT NULL,
	"direction" text DEFAULT 'outbound' NOT NULL,
	"subject" text,
	"content" text,
	"outcome" text,
	"priority" "interaction_priority" DEFAULT 'medium',
	"status" text DEFAULT 'completed',
	"scheduled_at" timestamp with time zone,
	"completed_at" timestamp with time zone,
	"follow_up_date" date,
	"tags" text[],
	"attachments" jsonb DEFAULT '[]'::jsonb,
	"created_by" uuid,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "customer_interactions" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "blog_categories" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" jsonb NOT NULL,
	"slug" text NOT NULL,
	"description" jsonb,
	"color" text DEFAULT '#3B82F6',
	"icon" text,
	"sort_order" integer DEFAULT 0,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "blog_categories_slug_key" UNIQUE("slug")
);
--> statement-breakpoint
ALTER TABLE "blog_categories" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "blog_tags" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" jsonb NOT NULL,
	"slug" text NOT NULL,
	"color" text DEFAULT '#6B7280',
	"usage_count" integer DEFAULT 0,
	"created_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "blog_tags_slug_key" UNIQUE("slug")
);
--> statement-breakpoint
ALTER TABLE "blog_tags" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "campaign_recipients" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"campaign_id" uuid,
	"patient_id" uuid,
	"email" text NOT NULL,
	"status" text DEFAULT 'pending',
	"sent_at" timestamp with time zone,
	"delivered_at" timestamp with time zone,
	"opened_at" timestamp with time zone,
	"clicked_at" timestamp with time zone,
	"bounce_reason" text,
	"created_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "campaign_recipients" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "cms_media" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"filename" text NOT NULL,
	"original_filename" text NOT NULL,
	"storage_path" text NOT NULL,
	"public_url" text,
	"mime_type" text NOT NULL,
	"file_size" integer NOT NULL,
	"width" integer,
	"height" integer,
	"alt_text" jsonb,
	"description" jsonb,
	"tags" text[],
	"is_active" boolean DEFAULT true,
	"uploaded_by" uuid,
	"created_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "cms_media" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "cms_pages" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"slug" text NOT NULL,
	"title" jsonb NOT NULL,
	"content" jsonb NOT NULL,
	"excerpt" jsonb,
	"meta_title" jsonb,
	"meta_description" jsonb,
	"meta_keywords" text[],
	"featured_image_url" text,
	"is_published" boolean DEFAULT false,
	"publish_at" timestamp with time zone,
	"page_type" text DEFAULT 'page' NOT NULL,
	"template" text DEFAULT 'default',
	"sort_order" integer DEFAULT 0,
	"view_count" integer DEFAULT 0,
	"created_by" uuid,
	"updated_by" uuid,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "cms_pages_slug_key" UNIQUE("slug")
);
--> statement-breakpoint
ALTER TABLE "cms_pages" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "doctor_services" (
	"id" uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
	"doctor_id" uuid,
	"service_id" uuid,
	"custom_price" numeric(10, 2),
	"is_available" boolean DEFAULT true,
	"created_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "doctor_services_doctor_id_service_id_key" UNIQUE("doctor_id","service_id")
);
--> statement-breakpoint
ALTER TABLE "doctor_services" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "email_templates" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"category" text NOT NULL,
	"subject" jsonb NOT NULL,
	"html_content" jsonb NOT NULL,
	"text_content" jsonb,
	"variables" text[],
	"is_active" boolean DEFAULT true,
	"usage_count" integer DEFAULT 0,
	"created_by" uuid,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "email_templates" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "inventory_items" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"category" text NOT NULL,
	"sku" text,
	"barcode" text,
	"unit_of_measure" text NOT NULL,
	"unit_cost" numeric(10, 2),
	"current_stock" integer DEFAULT 0,
	"minimum_stock" integer DEFAULT 0,
	"maximum_stock" integer,
	"reorder_point" integer,
	"supplier" text,
	"supplier_contact" text,
	"storage_location" text,
	"expiry_tracking" boolean DEFAULT false,
	"is_active" boolean DEFAULT true,
	"created_by" uuid,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "inventory_items_sku_key" UNIQUE("sku"),
	CONSTRAINT "inventory_items_barcode_key" UNIQUE("barcode")
);
--> statement-breakpoint
ALTER TABLE "inventory_items" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "inventory_transactions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"item_id" uuid,
	"transaction_type" text NOT NULL,
	"quantity" integer NOT NULL,
	"unit_cost" numeric(10, 2),
	"total_cost" numeric(10, 2),
	"reference_id" uuid,
	"reference_type" text,
	"notes" text,
	"batch_number" text,
	"expiry_date" date,
	"created_by" uuid,
	"created_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "inventory_transactions" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "kpi_metrics" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"date" date NOT NULL,
	"metric_name" text NOT NULL,
	"metric_value" numeric(15, 2) NOT NULL,
	"metric_unit" text,
	"category" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "kpi_metrics_date_metric_name_key" UNIQUE("date","metric_name")
);
--> statement-breakpoint
ALTER TABLE "kpi_metrics" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "marketing_campaigns" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"campaign_type" text NOT NULL,
	"target_segment" uuid,
	"email_template_id" uuid,
	"subject_line" jsonb,
	"content" jsonb NOT NULL,
	"sender_name" text,
	"sender_email" text,
	"scheduled_at" timestamp with time zone,
	"started_at" timestamp with time zone,
	"completed_at" timestamp with time zone,
	"status" "campaign_status" DEFAULT 'draft',
	"total_recipients" integer DEFAULT 0,
	"sent_count" integer DEFAULT 0,
	"delivered_count" integer DEFAULT 0,
	"opened_count" integer DEFAULT 0,
	"clicked_count" integer DEFAULT 0,
	"unsubscribed_count" integer DEFAULT 0,
	"bounced_count" integer DEFAULT 0,
	"budget_amount" numeric(10, 2),
	"actual_cost" numeric(10, 2),
	"roi_percentage" numeric(5, 2),
	"created_by" uuid,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "marketing_campaigns" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "website_metrics" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"date" date NOT NULL,
	"page_path" text NOT NULL,
	"page_views" integer DEFAULT 0,
	"unique_visitors" integer DEFAULT 0,
	"bounce_rate" numeric(5, 2) DEFAULT '0',
	"avg_session_duration" integer DEFAULT 0,
	"conversion_rate" numeric(5, 2) DEFAULT '0',
	"created_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "website_metrics_date_page_path_key" UNIQUE("date","page_path")
);
--> statement-breakpoint
ALTER TABLE "website_metrics" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "admin_activity_logs" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"admin_user_id" uuid,
	"performed_by" uuid,
	"action" text NOT NULL,
	"target_type" text NOT NULL,
	"target_id" uuid,
	"details" jsonb DEFAULT '{}'::jsonb,
	"ip_address" text,
	"user_agent" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "admin_activity_logs" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "appointment_types" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"duration_minutes" integer DEFAULT 60 NOT NULL,
	"price" numeric(10, 2),
	"color" text DEFAULT '#3B82F6',
	"is_active" boolean DEFAULT true,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "appointment_types" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "time_slots" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"doctor_id" uuid,
	"date" date NOT NULL,
	"start_time" time NOT NULL,
	"end_time" time NOT NULL,
	"is_available" boolean DEFAULT true,
	"is_recurring" boolean DEFAULT false,
	"recurring_pattern" jsonb,
	"created_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "time_slots" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "payload_migrations" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar,
	"batch" numeric,
	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "payload_locked_documents" (
	"id" serial PRIMARY KEY NOT NULL,
	"global_slug" varchar,
	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "payload_locked_documents_rels" (
	"id" serial PRIMARY KEY NOT NULL,
	"order" integer,
	"parent_id" integer NOT NULL,
	"path" varchar NOT NULL,
	"users_id" integer,
	"media_id" integer
);
--> statement-breakpoint
CREATE TABLE "users" (
	"id" serial PRIMARY KEY NOT NULL,
	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
	"email" varchar NOT NULL,
	"reset_password_token" varchar,
	"reset_password_expiration" timestamp(3) with time zone,
	"salt" varchar,
	"hash" varchar,
	"login_attempts" numeric DEFAULT '0',
	"lock_until" timestamp(3) with time zone
);
--> statement-breakpoint
CREATE TABLE "payload_preferences" (
	"id" serial PRIMARY KEY NOT NULL,
	"key" varchar,
	"value" jsonb,
	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "payload_preferences_rels" (
	"id" serial PRIMARY KEY NOT NULL,
	"order" integer,
	"parent_id" integer NOT NULL,
	"path" varchar NOT NULL,
	"users_id" integer
);
--> statement-breakpoint
CREATE TABLE "media" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"alt" text,
	"caption" text,
	"category" text,
	"prefix" text,
	"updated_at" timestamp with time zone DEFAULT now(),
	"created_at" timestamp with time zone DEFAULT now(),
	"url" text,
	"thumbnail_u_r_l" text,
	"filename" text,
	"mime_type" text,
	"filesize" integer,
	"width" integer,
	"height" integer,
	"focal_x" integer,
	"focal_y" integer,
	"sizes_thumbnail_url" text,
	"sizes_thumbnail_width" integer,
	"sizes_thumbnail_height" integer,
	"sizes_thumbnail_mime_type" text,
	"sizes_thumbnail_filesize" integer,
	"sizes_thumbnail_filename" text,
	"sizes_card_url" text,
	"sizes_card_width" integer,
	"sizes_card_height" integer,
	"sizes_card_mime_type" text,
	"sizes_card_filesize" integer,
	"sizes_card_filename" text,
	"sizes_tablet_url" text,
	"sizes_tablet_width" integer,
	"sizes_tablet_height" integer,
	"sizes_tablet_mime_type" text,
	"sizes_tablet_filesize" integer,
	"sizes_tablet_filename" text,
	"sizes_desktop_url" text,
	"sizes_desktop_width" integer,
	"sizes_desktop_height" integer,
	"sizes_desktop_mime_type" text,
	"sizes_desktop_filesize" integer,
	"sizes_desktop_filename" text
);
--> statement-breakpoint
CREATE TABLE "cms_page_categories" (
	"page_id" uuid NOT NULL,
	"category_id" uuid NOT NULL,
	CONSTRAINT "cms_page_categories_pkey" PRIMARY KEY("page_id","category_id")
);
--> statement-breakpoint
ALTER TABLE "cms_page_categories" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "cms_page_tags" (
	"page_id" uuid NOT NULL,
	"tag_id" uuid NOT NULL,
	CONSTRAINT "cms_page_tags_pkey" PRIMARY KEY("page_id","tag_id")
);
--> statement-breakpoint
ALTER TABLE "cms_page_tags" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "segment_memberships" (
	"segment_id" uuid NOT NULL,
	"patient_id" uuid NOT NULL,
	"added_by" uuid,
	"added_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "segment_memberships_pkey" PRIMARY KEY("segment_id","patient_id")
);
--> statement-breakpoint
ALTER TABLE "segment_memberships" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "doctors" ADD CONSTRAINT "doctors_id_fkey" FOREIGN KEY ("id") REFERENCES "public"."user_profiles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "services" ADD CONSTRAINT "services_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."service_categories"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "appointment_slots" ADD CONSTRAINT "appointment_slots_doctor_id_fkey" FOREIGN KEY ("doctor_id") REFERENCES "public"."doctors"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "appointments" ADD CONSTRAINT "appointments_appointment_type_id_fkey" FOREIGN KEY ("appointment_type_id") REFERENCES "public"."appointment_types"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "appointments" ADD CONSTRAINT "appointments_doctor_id_fkey" FOREIGN KEY ("doctor_id") REFERENCES "public"."doctors"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "appointments" ADD CONSTRAINT "appointments_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."user_profiles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "appointments" ADD CONSTRAINT "appointments_service_id_fkey" FOREIGN KEY ("service_id") REFERENCES "public"."services"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "appointments" ADD CONSTRAINT "appointments_slot_id_fkey" FOREIGN KEY ("slot_id") REFERENCES "public"."appointment_slots"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "invoices" ADD CONSTRAINT "invoices_appointment_id_fkey" FOREIGN KEY ("appointment_id") REFERENCES "public"."appointments"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "invoices" ADD CONSTRAINT "invoices_payment_id_fkey" FOREIGN KEY ("payment_id") REFERENCES "public"."payments"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "payments" ADD CONSTRAINT "payments_appointment_id_fkey" FOREIGN KEY ("appointment_id") REFERENCES "public"."appointments"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "payments" ADD CONSTRAINT "payments_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."user_profiles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "clinic_settings" ADD CONSTRAINT "clinic_settings_updated_by_fkey" FOREIGN KEY ("updated_by") REFERENCES "public"."user_profiles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "reviews" ADD CONSTRAINT "reviews_appointment_id_fkey" FOREIGN KEY ("appointment_id") REFERENCES "public"."appointments"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "reviews" ADD CONSTRAINT "reviews_doctor_id_fkey" FOREIGN KEY ("doctor_id") REFERENCES "public"."doctors"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "reviews" ADD CONSTRAINT "reviews_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."user_profiles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "reviews" ADD CONSTRAINT "reviews_service_id_fkey" FOREIGN KEY ("service_id") REFERENCES "public"."services"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "blog_posts" ADD CONSTRAINT "blog_posts_author_id_fkey" FOREIGN KEY ("author_id") REFERENCES "public"."user_profiles"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_appointment_id_fkey" FOREIGN KEY ("appointment_id") REFERENCES "public"."appointments"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_recipient_id_fkey" FOREIGN KEY ("recipient_id") REFERENCES "public"."user_profiles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "promotion_usage" ADD CONSTRAINT "promotion_usage_appointment_id_fkey" FOREIGN KEY ("appointment_id") REFERENCES "public"."appointments"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "promotion_usage" ADD CONSTRAINT "promotion_usage_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."user_profiles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "promotion_usage" ADD CONSTRAINT "promotion_usage_promotion_id_fkey" FOREIGN KEY ("promotion_id") REFERENCES "public"."promotions"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "customer_segments" ADD CONSTRAINT "customer_segments_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "public"."admin_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "admin_profiles" ADD CONSTRAINT "admin_profiles_id_fkey" FOREIGN KEY ("id") REFERENCES "public"."admin_users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_profiles" ADD CONSTRAINT "user_profiles_id_fkey" FOREIGN KEY ("id") REFERENCES "auth"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "customer_notes" ADD CONSTRAINT "customer_notes_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "public"."admin_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "customer_notes" ADD CONSTRAINT "customer_notes_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."user_profiles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "admin_users" ADD CONSTRAINT "admin_users_id_fkey" FOREIGN KEY ("id") REFERENCES "auth"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "analytics_events" ADD CONSTRAINT "analytics_events_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."user_profiles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "customer_interactions" ADD CONSTRAINT "customer_interactions_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "public"."admin_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "customer_interactions" ADD CONSTRAINT "customer_interactions_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."user_profiles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "campaign_recipients" ADD CONSTRAINT "campaign_recipients_campaign_id_fkey" FOREIGN KEY ("campaign_id") REFERENCES "public"."marketing_campaigns"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "campaign_recipients" ADD CONSTRAINT "campaign_recipients_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."user_profiles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "cms_media" ADD CONSTRAINT "cms_media_uploaded_by_fkey" FOREIGN KEY ("uploaded_by") REFERENCES "public"."admin_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "cms_pages" ADD CONSTRAINT "cms_pages_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "public"."admin_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "cms_pages" ADD CONSTRAINT "cms_pages_updated_by_fkey" FOREIGN KEY ("updated_by") REFERENCES "public"."admin_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "doctor_services" ADD CONSTRAINT "doctor_services_doctor_id_fkey" FOREIGN KEY ("doctor_id") REFERENCES "public"."doctors"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "doctor_services" ADD CONSTRAINT "doctor_services_service_id_fkey" FOREIGN KEY ("service_id") REFERENCES "public"."services"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "email_templates" ADD CONSTRAINT "email_templates_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "public"."admin_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "inventory_items" ADD CONSTRAINT "inventory_items_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "public"."admin_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "inventory_transactions" ADD CONSTRAINT "inventory_transactions_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "public"."admin_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "inventory_transactions" ADD CONSTRAINT "inventory_transactions_item_id_fkey" FOREIGN KEY ("item_id") REFERENCES "public"."inventory_items"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "marketing_campaigns" ADD CONSTRAINT "marketing_campaigns_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "public"."admin_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "marketing_campaigns" ADD CONSTRAINT "marketing_campaigns_email_template_id_fkey" FOREIGN KEY ("email_template_id") REFERENCES "public"."email_templates"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "marketing_campaigns" ADD CONSTRAINT "marketing_campaigns_target_segment_fkey" FOREIGN KEY ("target_segment") REFERENCES "public"."customer_segments"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "admin_activity_logs" ADD CONSTRAINT "admin_activity_logs_admin_user_id_fkey" FOREIGN KEY ("admin_user_id") REFERENCES "public"."admin_users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "admin_activity_logs" ADD CONSTRAINT "admin_activity_logs_performed_by_fkey" FOREIGN KEY ("performed_by") REFERENCES "auth"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "time_slots" ADD CONSTRAINT "time_slots_doctor_id_fkey" FOREIGN KEY ("doctor_id") REFERENCES "public"."user_profiles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."payload_locked_documents"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_users_fk" FOREIGN KEY ("users_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "payload_preferences_rels" ADD CONSTRAINT "payload_preferences_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."payload_preferences"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "payload_preferences_rels" ADD CONSTRAINT "payload_preferences_rels_users_fk" FOREIGN KEY ("users_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "cms_page_categories" ADD CONSTRAINT "cms_page_categories_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."blog_categories"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "cms_page_categories" ADD CONSTRAINT "cms_page_categories_page_id_fkey" FOREIGN KEY ("page_id") REFERENCES "public"."cms_pages"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "cms_page_tags" ADD CONSTRAINT "cms_page_tags_page_id_fkey" FOREIGN KEY ("page_id") REFERENCES "public"."cms_pages"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "cms_page_tags" ADD CONSTRAINT "cms_page_tags_tag_id_fkey" FOREIGN KEY ("tag_id") REFERENCES "public"."blog_tags"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "segment_memberships" ADD CONSTRAINT "segment_memberships_added_by_fkey" FOREIGN KEY ("added_by") REFERENCES "public"."admin_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "segment_memberships" ADD CONSTRAINT "segment_memberships_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."user_profiles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "segment_memberships" ADD CONSTRAINT "segment_memberships_segment_id_fkey" FOREIGN KEY ("segment_id") REFERENCES "public"."customer_segments"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "idx_services_category_id" ON "services" USING btree ("category_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "idx_services_is_active" ON "services" USING btree ("is_active" bool_ops);--> statement-breakpoint
CREATE INDEX "idx_services_is_featured" ON "services" USING btree ("is_featured" bool_ops);--> statement-breakpoint
CREATE INDEX "idx_services_is_popular" ON "services" USING btree ("is_popular" bool_ops);--> statement-breakpoint
CREATE INDEX "idx_services_slug" ON "services" USING btree ("slug" text_ops);--> statement-breakpoint
CREATE INDEX "idx_appointments_created_at" ON "appointments" USING btree ("created_at" timestamptz_ops);--> statement-breakpoint
CREATE INDEX "idx_appointments_date" ON "appointments" USING btree ("appointment_date" timestamptz_ops);--> statement-breakpoint
CREATE INDEX "idx_appointments_doctor_id" ON "appointments" USING btree ("doctor_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "idx_appointments_patient_id" ON "appointments" USING btree ("patient_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "idx_appointments_service_id" ON "appointments" USING btree ("service_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "idx_appointments_status" ON "appointments" USING btree ("status" enum_ops);--> statement-breakpoint
CREATE INDEX "idx_user_profiles_created_at" ON "user_profiles" USING btree ("created_at" timestamptz_ops);--> statement-breakpoint
CREATE INDEX "idx_user_profiles_email" ON "user_profiles" USING btree ("email" text_ops);--> statement-breakpoint
CREATE INDEX "idx_user_profiles_phone" ON "user_profiles" USING btree ("phone" text_ops);--> statement-breakpoint
CREATE INDEX "idx_user_profiles_role" ON "user_profiles" USING btree ("role" enum_ops);--> statement-breakpoint
CREATE INDEX "idx_customer_notes_patient" ON "customer_notes" USING btree ("patient_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "idx_customer_notes_type" ON "customer_notes" USING btree ("note_type" text_ops);--> statement-breakpoint
CREATE INDEX "idx_admin_users_department" ON "admin_users" USING btree ("department" text_ops);--> statement-breakpoint
CREATE INDEX "idx_admin_users_is_active" ON "admin_users" USING btree ("is_active" bool_ops);--> statement-breakpoint
CREATE INDEX "idx_admin_users_role" ON "admin_users" USING btree ("role" enum_ops);--> statement-breakpoint
CREATE INDEX "idx_analytics_events_category" ON "analytics_events" USING btree ("event_category" text_ops);--> statement-breakpoint
CREATE INDEX "idx_analytics_events_created_at" ON "analytics_events" USING btree ("created_at" timestamptz_ops);--> statement-breakpoint
CREATE INDEX "idx_analytics_events_patient" ON "analytics_events" USING btree ("patient_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "idx_analytics_events_type" ON "analytics_events" USING btree ("event_type" text_ops);--> statement-breakpoint
CREATE INDEX "idx_customer_interactions_created_at" ON "customer_interactions" USING btree ("created_at" timestamptz_ops);--> statement-breakpoint
CREATE INDEX "idx_customer_interactions_patient" ON "customer_interactions" USING btree ("patient_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "idx_customer_interactions_type" ON "customer_interactions" USING btree ("interaction_type" text_ops);--> statement-breakpoint
CREATE INDEX "idx_campaign_recipients_campaign" ON "campaign_recipients" USING btree ("campaign_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "idx_campaign_recipients_status" ON "campaign_recipients" USING btree ("status" text_ops);--> statement-breakpoint
CREATE INDEX "idx_cms_media_active" ON "cms_media" USING btree ("is_active" bool_ops);--> statement-breakpoint
CREATE INDEX "idx_cms_media_mime_type" ON "cms_media" USING btree ("mime_type" text_ops);--> statement-breakpoint
CREATE INDEX "idx_cms_pages_created_at" ON "cms_pages" USING btree ("created_at" timestamptz_ops);--> statement-breakpoint
CREATE INDEX "idx_cms_pages_published" ON "cms_pages" USING btree ("is_published" bool_ops);--> statement-breakpoint
CREATE INDEX "idx_cms_pages_slug" ON "cms_pages" USING btree ("slug" text_ops);--> statement-breakpoint
CREATE INDEX "idx_cms_pages_type" ON "cms_pages" USING btree ("page_type" text_ops);--> statement-breakpoint
CREATE INDEX "idx_inventory_items_category" ON "inventory_items" USING btree ("category" text_ops);--> statement-breakpoint
CREATE INDEX "idx_inventory_items_low_stock" ON "inventory_items" USING btree ("current_stock" int4_ops) WHERE (current_stock <= minimum_stock);--> statement-breakpoint
CREATE INDEX "idx_inventory_items_sku" ON "inventory_items" USING btree ("sku" text_ops);--> statement-breakpoint
CREATE INDEX "idx_inventory_transactions_created_at" ON "inventory_transactions" USING btree ("created_at" timestamptz_ops);--> statement-breakpoint
CREATE INDEX "idx_inventory_transactions_item" ON "inventory_transactions" USING btree ("item_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "idx_inventory_transactions_type" ON "inventory_transactions" USING btree ("transaction_type" text_ops);--> statement-breakpoint
CREATE INDEX "idx_kpi_metrics_date" ON "kpi_metrics" USING btree ("date" date_ops);--> statement-breakpoint
CREATE INDEX "idx_marketing_campaigns_scheduled" ON "marketing_campaigns" USING btree ("scheduled_at" timestamptz_ops);--> statement-breakpoint
CREATE INDEX "idx_marketing_campaigns_status" ON "marketing_campaigns" USING btree ("status" enum_ops);--> statement-breakpoint
CREATE INDEX "idx_website_metrics_date" ON "website_metrics" USING btree ("date" date_ops);--> statement-breakpoint
CREATE INDEX "idx_admin_activity_logs_admin_user_id" ON "admin_activity_logs" USING btree ("admin_user_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "idx_admin_activity_logs_created_at" ON "admin_activity_logs" USING btree ("created_at" timestamptz_ops);--> statement-breakpoint
CREATE INDEX "idx_admin_activity_logs_performed_by" ON "admin_activity_logs" USING btree ("performed_by" uuid_ops);--> statement-breakpoint
CREATE INDEX "payload_migrations_created_at_idx" ON "payload_migrations" USING btree ("created_at" timestamptz_ops);--> statement-breakpoint
CREATE INDEX "payload_migrations_updated_at_idx" ON "payload_migrations" USING btree ("updated_at" timestamptz_ops);--> statement-breakpoint
CREATE INDEX "payload_locked_documents_created_at_idx" ON "payload_locked_documents" USING btree ("created_at" timestamptz_ops);--> statement-breakpoint
CREATE INDEX "payload_locked_documents_global_slug_idx" ON "payload_locked_documents" USING btree ("global_slug" text_ops);--> statement-breakpoint
CREATE INDEX "payload_locked_documents_updated_at_idx" ON "payload_locked_documents" USING btree ("updated_at" timestamptz_ops);--> statement-breakpoint
CREATE INDEX "payload_locked_documents_rels_media_id_idx" ON "payload_locked_documents_rels" USING btree ("media_id" int4_ops);--> statement-breakpoint
CREATE INDEX "payload_locked_documents_rels_order_idx" ON "payload_locked_documents_rels" USING btree ("order" int4_ops);--> statement-breakpoint
CREATE INDEX "payload_locked_documents_rels_parent_idx" ON "payload_locked_documents_rels" USING btree ("parent_id" int4_ops);--> statement-breakpoint
CREATE INDEX "payload_locked_documents_rels_path_idx" ON "payload_locked_documents_rels" USING btree ("path" text_ops);--> statement-breakpoint
CREATE INDEX "payload_locked_documents_rels_users_id_idx" ON "payload_locked_documents_rels" USING btree ("users_id" int4_ops);--> statement-breakpoint
CREATE INDEX "users_created_at_idx" ON "users" USING btree ("created_at" timestamptz_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "users_email_idx" ON "users" USING btree ("email" text_ops);--> statement-breakpoint
CREATE INDEX "users_updated_at_idx" ON "users" USING btree ("updated_at" timestamptz_ops);--> statement-breakpoint
CREATE INDEX "payload_preferences_created_at_idx" ON "payload_preferences" USING btree ("created_at" timestamptz_ops);--> statement-breakpoint
CREATE INDEX "payload_preferences_key_idx" ON "payload_preferences" USING btree ("key" text_ops);--> statement-breakpoint
CREATE INDEX "payload_preferences_updated_at_idx" ON "payload_preferences" USING btree ("updated_at" timestamptz_ops);--> statement-breakpoint
CREATE INDEX "payload_preferences_rels_order_idx" ON "payload_preferences_rels" USING btree ("order" int4_ops);--> statement-breakpoint
CREATE INDEX "payload_preferences_rels_parent_idx" ON "payload_preferences_rels" USING btree ("parent_id" int4_ops);--> statement-breakpoint
CREATE INDEX "payload_preferences_rels_path_idx" ON "payload_preferences_rels" USING btree ("path" text_ops);--> statement-breakpoint
CREATE INDEX "payload_preferences_rels_users_id_idx" ON "payload_preferences_rels" USING btree ("users_id" int4_ops);--> statement-breakpoint
CREATE POLICY "Service categories are public" ON "service_categories" AS PERMISSIVE FOR SELECT TO public USING (true);--> statement-breakpoint
CREATE POLICY "Services are viewable by everyone" ON "services" AS PERMISSIVE FOR SELECT TO public USING (true);--> statement-breakpoint
CREATE POLICY "Admin can manage all appointments" ON "appointments" AS PERMISSIVE FOR ALL TO public USING ((EXISTS ( SELECT 1
   FROM admin_users
  WHERE ((admin_users.id = auth.uid()) AND (admin_users.is_active = true)))));--> statement-breakpoint
CREATE POLICY "Admins can manage all appointments" ON "appointments" AS PERMISSIVE FOR ALL TO public;--> statement-breakpoint
CREATE POLICY "Patients can create appointments" ON "appointments" AS PERMISSIVE FOR INSERT TO public;--> statement-breakpoint
CREATE POLICY "Patients can view own appointments" ON "appointments" AS PERMISSIVE FOR SELECT TO public;--> statement-breakpoint
CREATE POLICY "Patients can view their own appointments" ON "appointments" AS PERMISSIVE FOR SELECT TO public;--> statement-breakpoint
CREATE POLICY "Users can create appointments for themselves" ON "appointments" AS PERMISSIVE FOR INSERT TO public;--> statement-breakpoint
CREATE POLICY "Users can view their own appointments" ON "appointments" AS PERMISSIVE FOR SELECT TO public;--> statement-breakpoint
CREATE POLICY "Admins can insert invoices" ON "invoices" AS PERMISSIVE FOR INSERT TO public WITH CHECK (is_current_user_admin());--> statement-breakpoint
CREATE POLICY "Admins can insert payments" ON "payments" AS PERMISSIVE FOR INSERT TO public WITH CHECK (is_current_user_admin());--> statement-breakpoint
CREATE POLICY "Admins can view all payments" ON "payments" AS PERMISSIVE FOR ALL TO public;--> statement-breakpoint
CREATE POLICY "Users can view their own payments" ON "payments" AS PERMISSIVE FOR SELECT TO public;--> statement-breakpoint
CREATE POLICY "Admins can manage all reviews" ON "reviews" AS PERMISSIVE FOR ALL TO public USING ((EXISTS ( SELECT 1
   FROM admin_users
  WHERE ((admin_users.id = auth.uid()) AND (admin_users.is_active = true)))));--> statement-breakpoint
CREATE POLICY "Patients can create own reviews" ON "reviews" AS PERMISSIVE FOR INSERT TO public;--> statement-breakpoint
CREATE POLICY "Patients can view approved reviews" ON "reviews" AS PERMISSIVE FOR SELECT TO public;--> statement-breakpoint
CREATE POLICY "Users can create their own reviews" ON "reviews" AS PERMISSIVE FOR INSERT TO public;--> statement-breakpoint
CREATE POLICY "Users can update their own reviews" ON "reviews" AS PERMISSIVE FOR UPDATE TO public;--> statement-breakpoint
CREATE POLICY "Users can view their own reviews" ON "reviews" AS PERMISSIVE FOR SELECT TO public;--> statement-breakpoint
CREATE POLICY "Published blog posts are viewable by everyone" ON "blog_posts" AS PERMISSIVE FOR SELECT TO public USING ((is_published = true));--> statement-breakpoint
CREATE POLICY "Admin users can manage segments" ON "customer_segments" AS PERMISSIVE FOR ALL TO public USING (is_admin_user(auth.uid()));--> statement-breakpoint
CREATE POLICY "Admin users can view their own profile" ON "admin_profiles" AS PERMISSIVE FOR ALL TO public USING ((is_admin_user(auth.uid()) AND ((get_admin_role(auth.uid()) = ANY (ARRAY['super_admin'::admin_role, 'admin'::admin_role])) OR (auth.uid() = id))));--> statement-breakpoint
CREATE POLICY "Admins can view all user profiles" ON "user_profiles" AS PERMISSIVE FOR ALL TO public USING ((EXISTS ( SELECT 1
   FROM admin_users
  WHERE ((admin_users.id = auth.uid()) AND (admin_users.is_active = true)))));--> statement-breakpoint
CREATE POLICY "Users can create own profile" ON "user_profiles" AS PERMISSIVE FOR INSERT TO public;--> statement-breakpoint
CREATE POLICY "Users can update own profile" ON "user_profiles" AS PERMISSIVE FOR UPDATE TO public;--> statement-breakpoint
CREATE POLICY "Users can update their own profile" ON "user_profiles" AS PERMISSIVE FOR UPDATE TO public;--> statement-breakpoint
CREATE POLICY "Users can view own profile" ON "user_profiles" AS PERMISSIVE FOR SELECT TO public;--> statement-breakpoint
CREATE POLICY "Users can view their own profile" ON "user_profiles" AS PERMISSIVE FOR SELECT TO public;--> statement-breakpoint
CREATE POLICY "Admin users can manage customer notes" ON "customer_notes" AS PERMISSIVE FOR ALL TO public USING ((is_admin_user(auth.uid()) AND ((NOT is_private) OR (created_by = auth.uid()) OR (get_admin_role(auth.uid()) = ANY (ARRAY['super_admin'::admin_role, 'admin'::admin_role])))));--> statement-breakpoint
CREATE POLICY "Admins can manage all customer notes" ON "customer_notes" AS PERMISSIVE FOR ALL TO public;--> statement-breakpoint
CREATE POLICY "Users can view notes about themselves" ON "customer_notes" AS PERMISSIVE FOR SELECT TO public;--> statement-breakpoint
CREATE POLICY "Admins can delete admin users" ON "admin_users" AS PERMISSIVE FOR DELETE TO public USING (is_current_user_admin());--> statement-breakpoint
CREATE POLICY "Admins can insert admin users" ON "admin_users" AS PERMISSIVE FOR INSERT TO public;--> statement-breakpoint
CREATE POLICY "Admins can update admin users" ON "admin_users" AS PERMISSIVE FOR UPDATE TO public;--> statement-breakpoint
CREATE POLICY "Admins can view admin users" ON "admin_users" AS PERMISSIVE FOR SELECT TO public;--> statement-breakpoint
CREATE POLICY "Super admins and managers can manage admin users" ON "admin_users" AS PERMISSIVE FOR ALL TO public;--> statement-breakpoint
CREATE POLICY "Admin users can view analytics" ON "analytics_events" AS PERMISSIVE FOR SELECT TO public USING (is_admin_user(auth.uid()));--> statement-breakpoint
CREATE POLICY "Admin users can manage interactions" ON "customer_interactions" AS PERMISSIVE FOR ALL TO public USING (is_admin_user(auth.uid()));--> statement-breakpoint
CREATE POLICY "Admins can manage all customer interactions" ON "customer_interactions" AS PERMISSIVE FOR ALL TO public;--> statement-breakpoint
CREATE POLICY "Users can view their own interactions" ON "customer_interactions" AS PERMISSIVE FOR SELECT TO public;--> statement-breakpoint
CREATE POLICY "Admin users can manage categories" ON "blog_categories" AS PERMISSIVE FOR ALL TO public USING (is_admin_user(auth.uid()));--> statement-breakpoint
CREATE POLICY "Admin users can manage tags" ON "blog_tags" AS PERMISSIVE FOR ALL TO public USING (is_admin_user(auth.uid()));--> statement-breakpoint
CREATE POLICY "Admins can insert blog tags" ON "blog_tags" AS PERMISSIVE FOR INSERT TO public;--> statement-breakpoint
CREATE POLICY "Admin users can view recipients" ON "campaign_recipients" AS PERMISSIVE FOR SELECT TO public USING (is_admin_user(auth.uid()));--> statement-breakpoint
CREATE POLICY "Admin users can manage media" ON "cms_media" AS PERMISSIVE FOR ALL TO public USING (is_admin_user(auth.uid()));--> statement-breakpoint
CREATE POLICY "Admin users can manage content" ON "cms_pages" AS PERMISSIVE FOR ALL TO public USING (is_admin_user(auth.uid()));--> statement-breakpoint
CREATE POLICY "Admin users can manage templates" ON "email_templates" AS PERMISSIVE FOR ALL TO public USING (is_admin_user(auth.uid()));--> statement-breakpoint
CREATE POLICY "Admin users can manage inventory" ON "inventory_items" AS PERMISSIVE FOR ALL TO public USING (is_admin_user(auth.uid()));--> statement-breakpoint
CREATE POLICY "Admin users can manage transactions" ON "inventory_transactions" AS PERMISSIVE FOR ALL TO public USING (is_admin_user(auth.uid()));--> statement-breakpoint
CREATE POLICY "Admin users can manage kpis" ON "kpi_metrics" AS PERMISSIVE FOR ALL TO public USING (is_admin_user(auth.uid()));--> statement-breakpoint
CREATE POLICY "Admin users can manage campaigns" ON "marketing_campaigns" AS PERMISSIVE FOR ALL TO public USING (is_admin_user(auth.uid()));--> statement-breakpoint
CREATE POLICY "Admin users can view metrics" ON "website_metrics" AS PERMISSIVE FOR SELECT TO public USING (is_admin_user(auth.uid()));--> statement-breakpoint
CREATE POLICY "Admin users can create activity logs" ON "admin_activity_logs" AS PERMISSIVE FOR INSERT TO public WITH CHECK ((EXISTS ( SELECT 1
   FROM admin_users
  WHERE ((admin_users.id = auth.uid()) AND (admin_users.is_active = true)))));--> statement-breakpoint
CREATE POLICY "Admin users can view activity logs" ON "admin_activity_logs" AS PERMISSIVE FOR SELECT TO public;--> statement-breakpoint
CREATE POLICY "Admin can manage appointment types" ON "appointment_types" AS PERMISSIVE FOR ALL TO public USING ((EXISTS ( SELECT 1
   FROM admin_users
  WHERE ((admin_users.id = auth.uid()) AND (admin_users.is_active = true)))));--> statement-breakpoint
CREATE POLICY "Anyone can view appointment types" ON "appointment_types" AS PERMISSIVE FOR SELECT TO public;--> statement-breakpoint
CREATE POLICY "Admin can manage time slots" ON "time_slots" AS PERMISSIVE FOR ALL TO public USING ((EXISTS ( SELECT 1
   FROM admin_users
  WHERE ((admin_users.id = auth.uid()) AND (admin_users.is_active = true)))));--> statement-breakpoint
CREATE POLICY "Anyone can view available time slots" ON "time_slots" AS PERMISSIVE FOR SELECT TO public;
*/