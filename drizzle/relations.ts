import { relations } from "drizzle-orm/relations";
import { userProfiles, doctors, serviceCategories, services, appointmentSlots, appointmentTypes, appointments, invoices, payments, clinicSettings, reviews, blogPosts, notifications, promotionUsage, promotions, adminUsers, customerSegments, adminProfiles, usersInAuth, customerNotes, analyticsEvents, customerInteractions, marketingCampaigns, campaignRecipients, cmsMedia, cmsPages, doctorServices, emailTemplates, inventoryItems, inventoryTransactions, adminActivityLogs, timeSlots, payloadLockedDocuments, payloadLockedDocumentsRels, users, payloadPreferences, payloadPreferencesRels, blogCategories, cmsPageCategories, cmsPageTags, blogTags, segmentMemberships } from "./schema";

export const doctorsRelations = relations(doctors, ({one, many}) => ({
	userProfile: one(userProfiles, {
		fields: [doctors.id],
		references: [userProfiles.id]
	}),
	appointmentSlots: many(appointmentSlots),
	appointments: many(appointments),
	reviews: many(reviews),
	doctorServices: many(doctorServices),
}));

export const userProfilesRelations = relations(userProfiles, ({one, many}) => ({
	doctors: many(doctors),
	appointments: many(appointments),
	payments: many(payments),
	clinicSettings: many(clinicSettings),
	reviews: many(reviews),
	blogPosts: many(blogPosts),
	notifications: many(notifications),
	promotionUsages: many(promotionUsage),
	usersInAuth: one(usersInAuth, {
		fields: [userProfiles.id],
		references: [usersInAuth.id]
	}),
	customerNotes: many(customerNotes),
	analyticsEvents: many(analyticsEvents),
	customerInteractions: many(customerInteractions),
	campaignRecipients: many(campaignRecipients),
	timeSlots: many(timeSlots),
	segmentMemberships: many(segmentMemberships),
}));

export const servicesRelations = relations(services, ({one, many}) => ({
	serviceCategory: one(serviceCategories, {
		fields: [services.categoryId],
		references: [serviceCategories.id]
	}),
	appointments: many(appointments),
	reviews: many(reviews),
	doctorServices: many(doctorServices),
}));

export const serviceCategoriesRelations = relations(serviceCategories, ({many}) => ({
	services: many(services),
}));

export const appointmentSlotsRelations = relations(appointmentSlots, ({one, many}) => ({
	doctor: one(doctors, {
		fields: [appointmentSlots.doctorId],
		references: [doctors.id]
	}),
	appointments: many(appointments),
}));

export const appointmentsRelations = relations(appointments, ({one, many}) => ({
	appointmentType: one(appointmentTypes, {
		fields: [appointments.appointmentTypeId],
		references: [appointmentTypes.id]
	}),
	doctor: one(doctors, {
		fields: [appointments.doctorId],
		references: [doctors.id]
	}),
	userProfile: one(userProfiles, {
		fields: [appointments.patientId],
		references: [userProfiles.id]
	}),
	service: one(services, {
		fields: [appointments.serviceId],
		references: [services.id]
	}),
	appointmentSlot: one(appointmentSlots, {
		fields: [appointments.slotId],
		references: [appointmentSlots.id]
	}),
	invoices: many(invoices),
	payments: many(payments),
	reviews: many(reviews),
	notifications: many(notifications),
	promotionUsages: many(promotionUsage),
}));

export const appointmentTypesRelations = relations(appointmentTypes, ({many}) => ({
	appointments: many(appointments),
}));

export const invoicesRelations = relations(invoices, ({one}) => ({
	appointment: one(appointments, {
		fields: [invoices.appointmentId],
		references: [appointments.id]
	}),
	payment: one(payments, {
		fields: [invoices.paymentId],
		references: [payments.id]
	}),
}));

export const paymentsRelations = relations(payments, ({one, many}) => ({
	invoices: many(invoices),
	appointment: one(appointments, {
		fields: [payments.appointmentId],
		references: [appointments.id]
	}),
	userProfile: one(userProfiles, {
		fields: [payments.patientId],
		references: [userProfiles.id]
	}),
}));

export const clinicSettingsRelations = relations(clinicSettings, ({one}) => ({
	userProfile: one(userProfiles, {
		fields: [clinicSettings.updatedBy],
		references: [userProfiles.id]
	}),
}));

export const reviewsRelations = relations(reviews, ({one}) => ({
	appointment: one(appointments, {
		fields: [reviews.appointmentId],
		references: [appointments.id]
	}),
	doctor: one(doctors, {
		fields: [reviews.doctorId],
		references: [doctors.id]
	}),
	userProfile: one(userProfiles, {
		fields: [reviews.patientId],
		references: [userProfiles.id]
	}),
	service: one(services, {
		fields: [reviews.serviceId],
		references: [services.id]
	}),
}));

export const blogPostsRelations = relations(blogPosts, ({one}) => ({
	userProfile: one(userProfiles, {
		fields: [blogPosts.authorId],
		references: [userProfiles.id]
	}),
}));

export const notificationsRelations = relations(notifications, ({one}) => ({
	appointment: one(appointments, {
		fields: [notifications.appointmentId],
		references: [appointments.id]
	}),
	userProfile: one(userProfiles, {
		fields: [notifications.recipientId],
		references: [userProfiles.id]
	}),
}));

export const promotionUsageRelations = relations(promotionUsage, ({one}) => ({
	appointment: one(appointments, {
		fields: [promotionUsage.appointmentId],
		references: [appointments.id]
	}),
	userProfile: one(userProfiles, {
		fields: [promotionUsage.patientId],
		references: [userProfiles.id]
	}),
	promotion: one(promotions, {
		fields: [promotionUsage.promotionId],
		references: [promotions.id]
	}),
}));

export const promotionsRelations = relations(promotions, ({many}) => ({
	promotionUsages: many(promotionUsage),
}));

export const customerSegmentsRelations = relations(customerSegments, ({one, many}) => ({
	adminUser: one(adminUsers, {
		fields: [customerSegments.createdBy],
		references: [adminUsers.id]
	}),
	marketingCampaigns: many(marketingCampaigns),
	segmentMemberships: many(segmentMemberships),
}));

export const adminUsersRelations = relations(adminUsers, ({one, many}) => ({
	customerSegments: many(customerSegments),
	adminProfiles: many(adminProfiles),
	customerNotes: many(customerNotes),
	usersInAuth: one(usersInAuth, {
		fields: [adminUsers.id],
		references: [usersInAuth.id]
	}),
	customerInteractions: many(customerInteractions),
	cmsMedias: many(cmsMedia),
	cmsPages_createdBy: many(cmsPages, {
		relationName: "cmsPages_createdBy_adminUsers_id"
	}),
	cmsPages_updatedBy: many(cmsPages, {
		relationName: "cmsPages_updatedBy_adminUsers_id"
	}),
	emailTemplates: many(emailTemplates),
	inventoryItems: many(inventoryItems),
	inventoryTransactions: many(inventoryTransactions),
	marketingCampaigns: many(marketingCampaigns),
	adminActivityLogs: many(adminActivityLogs),
	segmentMemberships: many(segmentMemberships),
}));

export const adminProfilesRelations = relations(adminProfiles, ({one}) => ({
	adminUser: one(adminUsers, {
		fields: [adminProfiles.id],
		references: [adminUsers.id]
	}),
}));

export const usersInAuthRelations = relations(usersInAuth, ({many}) => ({
	userProfiles: many(userProfiles),
	adminUsers: many(adminUsers),
	adminActivityLogs: many(adminActivityLogs),
}));

export const customerNotesRelations = relations(customerNotes, ({one}) => ({
	adminUser: one(adminUsers, {
		fields: [customerNotes.createdBy],
		references: [adminUsers.id]
	}),
	userProfile: one(userProfiles, {
		fields: [customerNotes.patientId],
		references: [userProfiles.id]
	}),
}));

export const analyticsEventsRelations = relations(analyticsEvents, ({one}) => ({
	userProfile: one(userProfiles, {
		fields: [analyticsEvents.patientId],
		references: [userProfiles.id]
	}),
}));

export const customerInteractionsRelations = relations(customerInteractions, ({one}) => ({
	adminUser: one(adminUsers, {
		fields: [customerInteractions.createdBy],
		references: [adminUsers.id]
	}),
	userProfile: one(userProfiles, {
		fields: [customerInteractions.patientId],
		references: [userProfiles.id]
	}),
}));

export const campaignRecipientsRelations = relations(campaignRecipients, ({one}) => ({
	marketingCampaign: one(marketingCampaigns, {
		fields: [campaignRecipients.campaignId],
		references: [marketingCampaigns.id]
	}),
	userProfile: one(userProfiles, {
		fields: [campaignRecipients.patientId],
		references: [userProfiles.id]
	}),
}));

export const marketingCampaignsRelations = relations(marketingCampaigns, ({one, many}) => ({
	campaignRecipients: many(campaignRecipients),
	adminUser: one(adminUsers, {
		fields: [marketingCampaigns.createdBy],
		references: [adminUsers.id]
	}),
	emailTemplate: one(emailTemplates, {
		fields: [marketingCampaigns.emailTemplateId],
		references: [emailTemplates.id]
	}),
	customerSegment: one(customerSegments, {
		fields: [marketingCampaigns.targetSegment],
		references: [customerSegments.id]
	}),
}));

export const cmsMediaRelations = relations(cmsMedia, ({one}) => ({
	adminUser: one(adminUsers, {
		fields: [cmsMedia.uploadedBy],
		references: [adminUsers.id]
	}),
}));

export const cmsPagesRelations = relations(cmsPages, ({one, many}) => ({
	adminUser_createdBy: one(adminUsers, {
		fields: [cmsPages.createdBy],
		references: [adminUsers.id],
		relationName: "cmsPages_createdBy_adminUsers_id"
	}),
	adminUser_updatedBy: one(adminUsers, {
		fields: [cmsPages.updatedBy],
		references: [adminUsers.id],
		relationName: "cmsPages_updatedBy_adminUsers_id"
	}),
	cmsPageCategories: many(cmsPageCategories),
	cmsPageTags: many(cmsPageTags),
}));

export const doctorServicesRelations = relations(doctorServices, ({one}) => ({
	doctor: one(doctors, {
		fields: [doctorServices.doctorId],
		references: [doctors.id]
	}),
	service: one(services, {
		fields: [doctorServices.serviceId],
		references: [services.id]
	}),
}));

export const emailTemplatesRelations = relations(emailTemplates, ({one, many}) => ({
	adminUser: one(adminUsers, {
		fields: [emailTemplates.createdBy],
		references: [adminUsers.id]
	}),
	marketingCampaigns: many(marketingCampaigns),
}));

export const inventoryItemsRelations = relations(inventoryItems, ({one, many}) => ({
	adminUser: one(adminUsers, {
		fields: [inventoryItems.createdBy],
		references: [adminUsers.id]
	}),
	inventoryTransactions: many(inventoryTransactions),
}));

export const inventoryTransactionsRelations = relations(inventoryTransactions, ({one}) => ({
	adminUser: one(adminUsers, {
		fields: [inventoryTransactions.createdBy],
		references: [adminUsers.id]
	}),
	inventoryItem: one(inventoryItems, {
		fields: [inventoryTransactions.itemId],
		references: [inventoryItems.id]
	}),
}));

export const adminActivityLogsRelations = relations(adminActivityLogs, ({one}) => ({
	adminUser: one(adminUsers, {
		fields: [adminActivityLogs.adminUserId],
		references: [adminUsers.id]
	}),
	usersInAuth: one(usersInAuth, {
		fields: [adminActivityLogs.performedBy],
		references: [usersInAuth.id]
	}),
}));

export const timeSlotsRelations = relations(timeSlots, ({one}) => ({
	userProfile: one(userProfiles, {
		fields: [timeSlots.doctorId],
		references: [userProfiles.id]
	}),
}));

export const payloadLockedDocumentsRelsRelations = relations(payloadLockedDocumentsRels, ({one}) => ({
	payloadLockedDocument: one(payloadLockedDocuments, {
		fields: [payloadLockedDocumentsRels.parentId],
		references: [payloadLockedDocuments.id]
	}),
	user: one(users, {
		fields: [payloadLockedDocumentsRels.usersId],
		references: [users.id]
	}),
}));

export const payloadLockedDocumentsRelations = relations(payloadLockedDocuments, ({many}) => ({
	payloadLockedDocumentsRels: many(payloadLockedDocumentsRels),
}));

export const usersRelations = relations(users, ({many}) => ({
	payloadLockedDocumentsRels: many(payloadLockedDocumentsRels),
	payloadPreferencesRels: many(payloadPreferencesRels),
}));

export const payloadPreferencesRelsRelations = relations(payloadPreferencesRels, ({one}) => ({
	payloadPreference: one(payloadPreferences, {
		fields: [payloadPreferencesRels.parentId],
		references: [payloadPreferences.id]
	}),
	user: one(users, {
		fields: [payloadPreferencesRels.usersId],
		references: [users.id]
	}),
}));

export const payloadPreferencesRelations = relations(payloadPreferences, ({many}) => ({
	payloadPreferencesRels: many(payloadPreferencesRels),
}));

export const cmsPageCategoriesRelations = relations(cmsPageCategories, ({one}) => ({
	blogCategory: one(blogCategories, {
		fields: [cmsPageCategories.categoryId],
		references: [blogCategories.id]
	}),
	cmsPage: one(cmsPages, {
		fields: [cmsPageCategories.pageId],
		references: [cmsPages.id]
	}),
}));

export const blogCategoriesRelations = relations(blogCategories, ({many}) => ({
	cmsPageCategories: many(cmsPageCategories),
}));

export const cmsPageTagsRelations = relations(cmsPageTags, ({one}) => ({
	cmsPage: one(cmsPages, {
		fields: [cmsPageTags.pageId],
		references: [cmsPages.id]
	}),
	blogTag: one(blogTags, {
		fields: [cmsPageTags.tagId],
		references: [blogTags.id]
	}),
}));

export const blogTagsRelations = relations(blogTags, ({many}) => ({
	cmsPageTags: many(cmsPageTags),
}));

export const segmentMembershipsRelations = relations(segmentMemberships, ({one}) => ({
	adminUser: one(adminUsers, {
		fields: [segmentMemberships.addedBy],
		references: [adminUsers.id]
	}),
	userProfile: one(userProfiles, {
		fields: [segmentMemberships.patientId],
		references: [userProfiles.id]
	}),
	customerSegment: one(customerSegments, {
		fields: [segmentMemberships.segmentId],
		references: [customerSegments.id]
	}),
}));