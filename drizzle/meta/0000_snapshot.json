{"id": "00000000-0000-0000-0000-000000000000", "prevId": "", "version": "7", "dialect": "postgresql", "tables": {"public.doctors": {"name": "doctors", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "license_number": {"name": "license_number", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "specialization": {"name": "specialization", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "qualification": {"name": "qualification", "type": "text", "primaryKey": false, "notNull": true}, "experience_years": {"name": "experience_years", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "consultation_fee": {"name": "consultation_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "languages_spoken": {"name": "languages_spoken", "type": "text[]", "primaryKey": false, "notNull": false}, "working_hours": {"name": "working_hours", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_available": {"name": "is_available", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "rating": {"name": "rating", "type": "numeric(3, 2)", "primaryKey": false, "notNull": false, "default": "'5.00'"}, "total_reviews": {"name": "total_reviews", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"doctors_id_fkey": {"name": "doctors_id_fkey", "tableFrom": "doctors", "tableTo": "user_profiles", "schemaTo": "public", "columnsFrom": ["id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"doctors_license_number_key": {"columns": ["license_number"], "nullsNotDistinct": false, "name": "doctors_license_number_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": true}, "public.service_categories": {"name": "service_categories", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v4()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"service_categories_slug_key": {"columns": ["slug"], "nullsNotDistinct": false, "name": "service_categories_slug_key"}}, "checkConstraints": {}, "policies": {"Service categories are public": {"name": "Service categories are public", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"], "using": "true"}}, "isRLSEnabled": true}, "public.services": {"name": "services", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v4()"}, "category_id": {"name": "category_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "short_description": {"name": "short_description", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "base_price": {"name": "base_price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "discounted_price": {"name": "discounted_price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "duration_minutes": {"name": "duration_minutes", "type": "integer", "primaryKey": false, "notNull": true, "default": 60}, "difficulty": {"name": "difficulty", "type": "treatment_difficulty", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'beginner'"}, "preparation_instructions": {"name": "preparation_instructions", "type": "text", "primaryKey": false, "notNull": false}, "aftercare_instructions": {"name": "aftercare_instructions", "type": "text", "primaryKey": false, "notNull": false}, "contraindications": {"name": "contraindications", "type": "text", "primaryKey": false, "notNull": false}, "benefits": {"name": "benefits", "type": "text[]", "primaryKey": false, "notNull": false}, "procedures": {"name": "procedures", "type": "text[]", "primaryKey": false, "notNull": false}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false}, "gallery_images": {"name": "gallery_images", "type": "text[]", "primaryKey": false, "notNull": false}, "is_popular": {"name": "is_popular", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_featured": {"name": "is_featured", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "seo_title": {"name": "seo_title", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "seo_description": {"name": "seo_description", "type": "<PERSON><PERSON><PERSON>(300)", "primaryKey": false, "notNull": false}, "seo_keywords": {"name": "seo_keywords", "type": "text[]", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_services_category_id": {"name": "idx_services_category_id", "columns": [{"expression": "category_id", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_services_is_active": {"name": "idx_services_is_active", "columns": [{"expression": "is_active", "asc": true, "nulls": "last", "opclass": "bool_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_services_is_featured": {"name": "idx_services_is_featured", "columns": [{"expression": "is_featured", "asc": true, "nulls": "last", "opclass": "bool_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_services_is_popular": {"name": "idx_services_is_popular", "columns": [{"expression": "is_popular", "asc": true, "nulls": "last", "opclass": "bool_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_services_slug": {"name": "idx_services_slug", "columns": [{"expression": "slug", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"services_category_id_fkey": {"name": "services_category_id_fkey", "tableFrom": "services", "tableTo": "service_categories", "schemaTo": "public", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"services_slug_key": {"columns": ["slug"], "nullsNotDistinct": false, "name": "services_slug_key"}}, "checkConstraints": {}, "policies": {"Services are viewable by everyone": {"name": "Services are viewable by everyone", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"], "using": "true"}}, "isRLSEnabled": true}, "public.appointment_slots": {"name": "appointment_slots", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v4()"}, "doctor_id": {"name": "doctor_id", "type": "uuid", "primaryKey": false, "notNull": false}, "start_time": {"name": "start_time", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "end_time": {"name": "end_time", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "is_available": {"name": "is_available", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "recurring_rule": {"name": "recurring_rule", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"appointment_slots_doctor_id_fkey": {"name": "appointment_slots_doctor_id_fkey", "tableFrom": "appointment_slots", "tableTo": "doctors", "schemaTo": "public", "columnsFrom": ["doctor_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": true}, "public.appointments": {"name": "appointments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v4()"}, "patient_id": {"name": "patient_id", "type": "uuid", "primaryKey": false, "notNull": false}, "doctor_id": {"name": "doctor_id", "type": "uuid", "primaryKey": false, "notNull": false}, "service_id": {"name": "service_id", "type": "uuid", "primaryKey": false, "notNull": false}, "slot_id": {"name": "slot_id", "type": "uuid", "primaryKey": false, "notNull": false}, "appointment_date": {"name": "appointment_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "duration_minutes": {"name": "duration_minutes", "type": "integer", "primaryKey": false, "notNull": true, "default": 60}, "status": {"name": "status", "type": "appointment_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'scheduled'"}, "total_amount": {"name": "total_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "deposit_amount": {"name": "deposit_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "patient_notes": {"name": "patient_notes", "type": "text", "primaryKey": false, "notNull": false}, "doctor_notes": {"name": "doctor_notes", "type": "text", "primaryKey": false, "notNull": false}, "treatment_plan": {"name": "treatment_plan", "type": "text", "primaryKey": false, "notNull": false}, "before_photos": {"name": "before_photos", "type": "text[]", "primaryKey": false, "notNull": false}, "after_photos": {"name": "after_photos", "type": "text[]", "primaryKey": false, "notNull": false}, "prescription": {"name": "prescription", "type": "text", "primaryKey": false, "notNull": false}, "next_appointment_recommended": {"name": "next_appointment_recommended", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "followup_date": {"name": "followup_date", "type": "date", "primaryKey": false, "notNull": false}, "confirmation_sent_at": {"name": "confirmation_sent_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "reminder_sent_at": {"name": "reminder_sent_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "checked_in_at": {"name": "checked_in_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "cancelled_at": {"name": "cancelled_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "cancellation_reason": {"name": "cancellation_reason", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "appointment_type_id": {"name": "appointment_type_id", "type": "uuid", "primaryKey": false, "notNull": false}, "patient_phone": {"name": "patient_phone", "type": "text", "primaryKey": false, "notNull": false}, "patient_email": {"name": "patient_email", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_appointments_created_at": {"name": "idx_appointments_created_at", "columns": [{"expression": "created_at", "asc": true, "nulls": "last", "opclass": "timestamptz_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_appointments_date": {"name": "idx_appointments_date", "columns": [{"expression": "appointment_date", "asc": true, "nulls": "last", "opclass": "timestamptz_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_appointments_doctor_id": {"name": "idx_appointments_doctor_id", "columns": [{"expression": "doctor_id", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_appointments_patient_id": {"name": "idx_appointments_patient_id", "columns": [{"expression": "patient_id", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_appointments_service_id": {"name": "idx_appointments_service_id", "columns": [{"expression": "service_id", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_appointments_status": {"name": "idx_appointments_status", "columns": [{"expression": "status", "asc": true, "nulls": "last", "opclass": "enum_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"appointments_appointment_type_id_fkey": {"name": "appointments_appointment_type_id_fkey", "tableFrom": "appointments", "tableTo": "appointment_types", "schemaTo": "public", "columnsFrom": ["appointment_type_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "appointments_doctor_id_fkey": {"name": "appointments_doctor_id_fkey", "tableFrom": "appointments", "tableTo": "doctors", "schemaTo": "public", "columnsFrom": ["doctor_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "appointments_patient_id_fkey": {"name": "appointments_patient_id_fkey", "tableFrom": "appointments", "tableTo": "user_profiles", "schemaTo": "public", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "appointments_service_id_fkey": {"name": "appointments_service_id_fkey", "tableFrom": "appointments", "tableTo": "services", "schemaTo": "public", "columnsFrom": ["service_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "appointments_slot_id_fkey": {"name": "appointments_slot_id_fkey", "tableFrom": "appointments", "tableTo": "appointment_slots", "schemaTo": "public", "columnsFrom": ["slot_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {"Admin can manage all appointments": {"name": "Admin can manage all appointments", "as": "PERMISSIVE", "for": "ALL", "to": ["public"], "using": "(EXISTS ( SELECT 1\n   FROM admin_users\n  WHERE ((admin_users.id = auth.uid()) AND (admin_users.is_active = true))))"}, "Admins can manage all appointments": {"name": "Admins can manage all appointments", "as": "PERMISSIVE", "for": "ALL", "to": ["public"]}, "Patients can create appointments": {"name": "Patients can create appointments", "as": "PERMISSIVE", "for": "INSERT", "to": ["public"]}, "Patients can view own appointments": {"name": "Patients can view own appointments", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"]}, "Patients can view their own appointments": {"name": "Patients can view their own appointments", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"]}, "Users can create appointments for themselves": {"name": "Users can create appointments for themselves", "as": "PERMISSIVE", "for": "INSERT", "to": ["public"]}, "Users can view their own appointments": {"name": "Users can view their own appointments", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"]}}, "isRLSEnabled": true}, "public.invoices": {"name": "invoices", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v4()"}, "appointment_id": {"name": "appointment_id", "type": "uuid", "primaryKey": false, "notNull": false}, "payment_id": {"name": "payment_id", "type": "uuid", "primaryKey": false, "notNull": false}, "invoice_number": {"name": "invoice_number", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "issue_date": {"name": "issue_date", "type": "date", "primaryKey": false, "notNull": true, "default": "CURRENT_DATE"}, "due_date": {"name": "due_date", "type": "date", "primaryKey": false, "notNull": false}, "subtotal": {"name": "subtotal", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "tax_amount": {"name": "tax_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "discount_amount": {"name": "discount_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "total_amount": {"name": "total_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": false, "default": "'THB'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "is_paid": {"name": "is_paid", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "pdf_url": {"name": "pdf_url", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"invoices_appointment_id_fkey": {"name": "invoices_appointment_id_fkey", "tableFrom": "invoices", "tableTo": "appointments", "schemaTo": "public", "columnsFrom": ["appointment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "invoices_payment_id_fkey": {"name": "invoices_payment_id_fkey", "tableFrom": "invoices", "tableTo": "payments", "schemaTo": "public", "columnsFrom": ["payment_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"invoices_invoice_number_key": {"columns": ["invoice_number"], "nullsNotDistinct": false, "name": "invoices_invoice_number_key"}}, "checkConstraints": {}, "policies": {"Admins can insert invoices": {"name": "Admins can insert invoices", "as": "PERMISSIVE", "for": "INSERT", "to": ["public"], "withCheck": "is_current_user_admin()"}}, "isRLSEnabled": true}, "public.payments": {"name": "payments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v4()"}, "appointment_id": {"name": "appointment_id", "type": "uuid", "primaryKey": false, "notNull": false}, "patient_id": {"name": "patient_id", "type": "uuid", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "payment_method": {"name": "payment_method", "type": "payment_method", "typeSchema": "public", "primaryKey": false, "notNull": true}, "payment_status": {"name": "payment_status", "type": "payment_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'pending'"}, "transaction_id": {"name": "transaction_id", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "stripe_payment_intent_id": {"name": "stripe_payment_intent_id", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "payment_date": {"name": "payment_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "refund_amount": {"name": "refund_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "refund_date": {"name": "refund_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "refund_reason": {"name": "refund_reason", "type": "text", "primaryKey": false, "notNull": false}, "receipt_url": {"name": "receipt_url", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"payments_appointment_id_fkey": {"name": "payments_appointment_id_fkey", "tableFrom": "payments", "tableTo": "appointments", "schemaTo": "public", "columnsFrom": ["appointment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payments_patient_id_fkey": {"name": "payments_patient_id_fkey", "tableFrom": "payments", "tableTo": "user_profiles", "schemaTo": "public", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"payments_transaction_id_key": {"columns": ["transaction_id"], "nullsNotDistinct": false, "name": "payments_transaction_id_key"}}, "checkConstraints": {}, "policies": {"Admins can insert payments": {"name": "Admins can insert payments", "as": "PERMISSIVE", "for": "INSERT", "to": ["public"], "withCheck": "is_current_user_admin()"}, "Admins can view all payments": {"name": "Admins can view all payments", "as": "PERMISSIVE", "for": "ALL", "to": ["public"]}, "Users can view their own payments": {"name": "Users can view their own payments", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"]}}, "isRLSEnabled": true}, "public.clinic_settings": {"name": "clinic_settings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v4()"}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "data_type": {"name": "data_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'string'"}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"clinic_settings_updated_by_fkey": {"name": "clinic_settings_updated_by_fkey", "tableFrom": "clinic_settings", "tableTo": "user_profiles", "schemaTo": "public", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"clinic_settings_key_key": {"columns": ["key"], "nullsNotDistinct": false, "name": "clinic_settings_key_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": true}, "public.promotions": {"name": "promotions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v4()"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "discount_type": {"name": "discount_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "discount_value": {"name": "discount_value", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "min_purchase_amount": {"name": "min_purchase_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "max_discount_amount": {"name": "max_discount_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "promo_code": {"name": "promo_code", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "usage_limit": {"name": "usage_limit", "type": "integer", "primaryKey": false, "notNull": false}, "usage_count": {"name": "usage_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "applicable_services": {"name": "applicable_services", "type": "uuid[]", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "is_featured": {"name": "is_featured", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false}, "terms_conditions": {"name": "terms_conditions", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"promotions_promo_code_key": {"columns": ["promo_code"], "nullsNotDistinct": false, "name": "promotions_promo_code_key"}}, "checkConstraints": {"promotions_discount_type_check": {"name": "promotions_discount_type_check", "value": "(discount_type)::text = ANY ((ARRAY['percentage'::character varying, 'fixed_amount'::character varying])::text[])"}}, "policies": {}, "isRLSEnabled": true}, "public.newsletter_subscribers": {"name": "newsletter_subscribers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v4()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "language": {"name": "language", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false, "default": "'th'"}, "subscribed_at": {"name": "subscribed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "unsubscribed_at": {"name": "unsubscribed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "subscriber_tags": {"name": "subscriber_tags", "type": "text[]", "primaryKey": false, "notNull": false}, "source": {"name": "source", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "preferences": {"name": "preferences", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"newsletter_subscribers_email_key": {"columns": ["email"], "nullsNotDistinct": false, "name": "newsletter_subscribers_email_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": true}, "public.reviews": {"name": "reviews", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v4()"}, "patient_id": {"name": "patient_id", "type": "uuid", "primaryKey": false, "notNull": false}, "doctor_id": {"name": "doctor_id", "type": "uuid", "primaryKey": false, "notNull": false}, "service_id": {"name": "service_id", "type": "uuid", "primaryKey": false, "notNull": false}, "appointment_id": {"name": "appointment_id", "type": "uuid", "primaryKey": false, "notNull": false}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": false}, "is_anonymous": {"name": "is_anonymous", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_approved": {"name": "is_approved", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_featured": {"name": "is_featured", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "helpful_count": {"name": "helpful_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "before_photo_url": {"name": "before_photo_url", "type": "text", "primaryKey": false, "notNull": false}, "after_photo_url": {"name": "after_photo_url", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"reviews_appointment_id_fkey": {"name": "reviews_appointment_id_fkey", "tableFrom": "reviews", "tableTo": "appointments", "schemaTo": "public", "columnsFrom": ["appointment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "reviews_doctor_id_fkey": {"name": "reviews_doctor_id_fkey", "tableFrom": "reviews", "tableTo": "doctors", "schemaTo": "public", "columnsFrom": ["doctor_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "reviews_patient_id_fkey": {"name": "reviews_patient_id_fkey", "tableFrom": "reviews", "tableTo": "user_profiles", "schemaTo": "public", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "reviews_service_id_fkey": {"name": "reviews_service_id_fkey", "tableFrom": "reviews", "tableTo": "services", "schemaTo": "public", "columnsFrom": ["service_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"reviews_patient_id_appointment_id_key": {"columns": ["patient_id", "appointment_id"], "nullsNotDistinct": false, "name": "reviews_patient_id_appointment_id_key"}}, "checkConstraints": {"reviews_rating_check": {"name": "reviews_rating_check", "value": "(rating >= 1) AND (rating <= 5)"}}, "policies": {"Admins can manage all reviews": {"name": "Admins can manage all reviews", "as": "PERMISSIVE", "for": "ALL", "to": ["public"], "using": "(EXISTS ( SELECT 1\n   FROM admin_users\n  WHERE ((admin_users.id = auth.uid()) AND (admin_users.is_active = true))))"}, "Patients can create own reviews": {"name": "Patients can create own reviews", "as": "PERMISSIVE", "for": "INSERT", "to": ["public"]}, "Patients can view approved reviews": {"name": "Patients can view approved reviews", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"]}, "Users can create their own reviews": {"name": "Users can create their own reviews", "as": "PERMISSIVE", "for": "INSERT", "to": ["public"]}, "Users can update their own reviews": {"name": "Users can update their own reviews", "as": "PERMISSIVE", "for": "UPDATE", "to": ["public"]}, "Users can view their own reviews": {"name": "Users can view their own reviews", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"]}}, "isRLSEnabled": true}, "public.blog_posts": {"name": "blog_posts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v4()"}, "author_id": {"name": "author_id", "type": "uuid", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(300)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(300)", "primaryKey": false, "notNull": true}, "excerpt": {"name": "excerpt", "type": "text", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "featured_image": {"name": "featured_image", "type": "text", "primaryKey": false, "notNull": false}, "gallery_images": {"name": "gallery_images", "type": "text[]", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "text[]", "primaryKey": false, "notNull": false}, "difficulty": {"name": "difficulty", "type": "treatment_difficulty", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'beginner'"}, "read_time_minutes": {"name": "read_time_minutes", "type": "integer", "primaryKey": false, "notNull": false, "default": 5}, "view_count": {"name": "view_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "like_count": {"name": "like_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_published": {"name": "is_published", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_featured": {"name": "is_featured", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "published_at": {"name": "published_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "seo_title": {"name": "seo_title", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "seo_description": {"name": "seo_description", "type": "<PERSON><PERSON><PERSON>(300)", "primaryKey": false, "notNull": false}, "seo_keywords": {"name": "seo_keywords", "type": "text[]", "primaryKey": false, "notNull": false}, "language": {"name": "language", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false, "default": "'th'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"blog_posts_author_id_fkey": {"name": "blog_posts_author_id_fkey", "tableFrom": "blog_posts", "tableTo": "user_profiles", "schemaTo": "public", "columnsFrom": ["author_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"blog_posts_slug_key": {"columns": ["slug"], "nullsNotDistinct": false, "name": "blog_posts_slug_key"}}, "checkConstraints": {}, "policies": {"Published blog posts are viewable by everyone": {"name": "Published blog posts are viewable by everyone", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"], "using": "(is_published = true)"}}, "isRLSEnabled": true}, "public.notifications": {"name": "notifications", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v4()"}, "recipient_id": {"name": "recipient_id", "type": "uuid", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "notification_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "channel": {"name": "channel", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "template_id": {"name": "template_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "appointment_id": {"name": "appointment_id", "type": "uuid", "primaryKey": false, "notNull": false}, "sent_at": {"name": "sent_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "delivery_status": {"name": "delivery_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"notifications_appointment_id_fkey": {"name": "notifications_appointment_id_fkey", "tableFrom": "notifications", "tableTo": "appointments", "schemaTo": "public", "columnsFrom": ["appointment_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "notifications_recipient_id_fkey": {"name": "notifications_recipient_id_fkey", "tableFrom": "notifications", "tableTo": "user_profiles", "schemaTo": "public", "columnsFrom": ["recipient_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {"notifications_channel_check": {"name": "notifications_channel_check", "value": "(channel)::text = ANY ((ARRAY['email'::character varying, 'sms'::character varying, 'push'::character varying, 'in_app'::character varying])::text[])"}}, "policies": {}, "isRLSEnabled": true}, "public.promotion_usage": {"name": "promotion_usage", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v4()"}, "promotion_id": {"name": "promotion_id", "type": "uuid", "primaryKey": false, "notNull": false}, "patient_id": {"name": "patient_id", "type": "uuid", "primaryKey": false, "notNull": false}, "appointment_id": {"name": "appointment_id", "type": "uuid", "primaryKey": false, "notNull": false}, "discount_amount": {"name": "discount_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "used_at": {"name": "used_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"promotion_usage_appointment_id_fkey": {"name": "promotion_usage_appointment_id_fkey", "tableFrom": "promotion_usage", "tableTo": "appointments", "schemaTo": "public", "columnsFrom": ["appointment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "promotion_usage_patient_id_fkey": {"name": "promotion_usage_patient_id_fkey", "tableFrom": "promotion_usage", "tableTo": "user_profiles", "schemaTo": "public", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "promotion_usage_promotion_id_fkey": {"name": "promotion_usage_promotion_id_fkey", "tableFrom": "promotion_usage", "tableTo": "promotions", "schemaTo": "public", "columnsFrom": ["promotion_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": true}, "public.customer_segments": {"name": "customer_segments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "criteria": {"name": "criteria", "type": "jsonb", "primaryKey": false, "notNull": true}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": false, "default": "'#3B82F6'"}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": false}, "is_dynamic": {"name": "is_dynamic", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "patient_count": {"name": "patient_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "last_updated_at": {"name": "last_updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"customer_segments_created_by_fkey": {"name": "customer_segments_created_by_fkey", "tableFrom": "customer_segments", "tableTo": "admin_users", "schemaTo": "public", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {"Admin users can manage segments": {"name": "Admin users can manage segments", "as": "PERMISSIVE", "for": "ALL", "to": ["public"], "using": "is_admin_user(auth.uid())"}}, "isRLSEnabled": true}, "public.admin_profiles": {"name": "admin_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "preferences": {"name": "preferences", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"admin_profiles_id_fkey": {"name": "admin_profiles_id_fkey", "tableFrom": "admin_profiles", "tableTo": "admin_users", "schemaTo": "public", "columnsFrom": ["id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"admin_profiles_email_key": {"columns": ["email"], "nullsNotDistinct": false, "name": "admin_profiles_email_key"}}, "checkConstraints": {}, "policies": {"Admin users can view their own profile": {"name": "Admin users can view their own profile", "as": "PERMISSIVE", "for": "ALL", "to": ["public"], "using": "(is_admin_user(auth.uid()) AND ((get_admin_role(auth.uid()) = ANY (ARRAY['super_admin'::admin_role, 'admin'::admin_role])) OR (auth.uid() = id)))"}}, "isRLSEnabled": true}, "public.user_profiles": {"name": "user_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "role": {"name": "role", "type": "user_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'patient'"}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "date_of_birth": {"name": "date_of_birth", "type": "date", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "default": "'Thailand'"}, "emergency_contact_name": {"name": "emergency_contact_name", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "emergency_contact_phone": {"name": "emergency_contact_phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "medical_history": {"name": "medical_history", "type": "text", "primaryKey": false, "notNull": false}, "allergies": {"name": "allergies", "type": "text", "primaryKey": false, "notNull": false}, "current_medications": {"name": "current_medications", "type": "text", "primaryKey": false, "notNull": false}, "preferred_language": {"name": "preferred_language", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false, "default": "'th'"}, "marketing_consent": {"name": "marketing_consent", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "privacy_consent": {"name": "privacy_consent", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "last_login": {"name": "last_login", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}}, "indexes": {"idx_user_profiles_created_at": {"name": "idx_user_profiles_created_at", "columns": [{"expression": "created_at", "asc": true, "nulls": "last", "opclass": "timestamptz_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_profiles_email": {"name": "idx_user_profiles_email", "columns": [{"expression": "email", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_profiles_phone": {"name": "idx_user_profiles_phone", "columns": [{"expression": "phone", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_profiles_role": {"name": "idx_user_profiles_role", "columns": [{"expression": "role", "asc": true, "nulls": "last", "opclass": "enum_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_profiles_id_fkey": {"name": "user_profiles_id_fkey", "tableFrom": "user_profiles", "tableTo": "users", "schemaTo": "auth", "columnsFrom": ["id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_profiles_email_key": {"columns": ["email"], "nullsNotDistinct": false, "name": "user_profiles_email_key"}}, "checkConstraints": {}, "policies": {"Admins can view all user profiles": {"name": "Admins can view all user profiles", "as": "PERMISSIVE", "for": "ALL", "to": ["public"], "using": "(EXISTS ( SELECT 1\n   FROM admin_users\n  WHERE ((admin_users.id = auth.uid()) AND (admin_users.is_active = true))))"}, "Users can create own profile": {"name": "Users can create own profile", "as": "PERMISSIVE", "for": "INSERT", "to": ["public"]}, "Users can update own profile": {"name": "Users can update own profile", "as": "PERMISSIVE", "for": "UPDATE", "to": ["public"]}, "Users can update their own profile": {"name": "Users can update their own profile", "as": "PERMISSIVE", "for": "UPDATE", "to": ["public"]}, "Users can view own profile": {"name": "Users can view own profile", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"]}, "Users can view their own profile": {"name": "Users can view their own profile", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"]}}, "isRLSEnabled": true}, "public.customer_notes": {"name": "customer_notes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "patient_id": {"name": "patient_id", "type": "uuid", "primaryKey": false, "notNull": false}, "note_type": {"name": "note_type", "type": "text", "primaryKey": false, "notNull": false, "default": "'general'"}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "is_private": {"name": "is_private", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_important": {"name": "is_important", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "tags": {"name": "tags", "type": "text[]", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_customer_notes_patient": {"name": "idx_customer_notes_patient", "columns": [{"expression": "patient_id", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_customer_notes_type": {"name": "idx_customer_notes_type", "columns": [{"expression": "note_type", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"customer_notes_created_by_fkey": {"name": "customer_notes_created_by_fkey", "tableFrom": "customer_notes", "tableTo": "admin_users", "schemaTo": "public", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "customer_notes_patient_id_fkey": {"name": "customer_notes_patient_id_fkey", "tableFrom": "customer_notes", "tableTo": "user_profiles", "schemaTo": "public", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {"Admin users can manage customer notes": {"name": "Admin users can manage customer notes", "as": "PERMISSIVE", "for": "ALL", "to": ["public"], "using": "(is_admin_user(auth.uid()) AND ((NOT is_private) OR (created_by = auth.uid()) OR (get_admin_role(auth.uid()) = ANY (ARRAY['super_admin'::admin_role, 'admin'::admin_role]))))"}, "Admins can manage all customer notes": {"name": "Admins can manage all customer notes", "as": "PERMISSIVE", "for": "ALL", "to": ["public"]}, "Users can view notes about themselves": {"name": "Users can view notes about themselves", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"]}}, "isRLSEnabled": true}, "public.admin_users": {"name": "admin_users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "role": {"name": "role", "type": "admin_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'staff'"}, "permissions": {"name": "permissions", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "department": {"name": "department", "type": "text", "primaryKey": false, "notNull": false}, "employee_id": {"name": "employee_id", "type": "text", "primaryKey": false, "notNull": false}, "hire_date": {"name": "hire_date", "type": "date", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "last_login_at": {"name": "last_login_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_admin_users_department": {"name": "idx_admin_users_department", "columns": [{"expression": "department", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_admin_users_is_active": {"name": "idx_admin_users_is_active", "columns": [{"expression": "is_active", "asc": true, "nulls": "last", "opclass": "bool_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_admin_users_role": {"name": "idx_admin_users_role", "columns": [{"expression": "role", "asc": true, "nulls": "last", "opclass": "enum_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"admin_users_id_fkey": {"name": "admin_users_id_fkey", "tableFrom": "admin_users", "tableTo": "users", "schemaTo": "auth", "columnsFrom": ["id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"admin_users_employee_id_key": {"columns": ["employee_id"], "nullsNotDistinct": false, "name": "admin_users_employee_id_key"}}, "checkConstraints": {}, "policies": {"Admins can delete admin users": {"name": "Admins can delete admin users", "as": "PERMISSIVE", "for": "DELETE", "to": ["public"], "using": "is_current_user_admin()"}, "Admins can insert admin users": {"name": "Admins can insert admin users", "as": "PERMISSIVE", "for": "INSERT", "to": ["public"]}, "Admins can update admin users": {"name": "Admins can update admin users", "as": "PERMISSIVE", "for": "UPDATE", "to": ["public"]}, "Admins can view admin users": {"name": "Admins can view admin users", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"]}, "Super admins and managers can manage admin users": {"name": "Super admins and managers can manage admin users", "as": "PERMISSIVE", "for": "ALL", "to": ["public"]}}, "isRLSEnabled": true}, "public.analytics_events": {"name": "analytics_events", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "event_type": {"name": "event_type", "type": "text", "primaryKey": false, "notNull": true}, "event_category": {"name": "event_category", "type": "text", "primaryKey": false, "notNull": true}, "event_data": {"name": "event_data", "type": "jsonb", "primaryKey": false, "notNull": true}, "patient_id": {"name": "patient_id", "type": "uuid", "primaryKey": false, "notNull": false}, "session_id": {"name": "session_id", "type": "text", "primaryKey": false, "notNull": false}, "page_url": {"name": "page_url", "type": "text", "primaryKey": false, "notNull": false}, "referrer_url": {"name": "referrer_url", "type": "text", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "inet", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "device_type": {"name": "device_type", "type": "text", "primaryKey": false, "notNull": false}, "browser": {"name": "browser", "type": "text", "primaryKey": false, "notNull": false}, "os": {"name": "os", "type": "text", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_analytics_events_category": {"name": "idx_analytics_events_category", "columns": [{"expression": "event_category", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_analytics_events_created_at": {"name": "idx_analytics_events_created_at", "columns": [{"expression": "created_at", "asc": false, "nulls": "first", "opclass": "timestamptz_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_analytics_events_patient": {"name": "idx_analytics_events_patient", "columns": [{"expression": "patient_id", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_analytics_events_type": {"name": "idx_analytics_events_type", "columns": [{"expression": "event_type", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"analytics_events_patient_id_fkey": {"name": "analytics_events_patient_id_fkey", "tableFrom": "analytics_events", "tableTo": "user_profiles", "schemaTo": "public", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {"Admin users can view analytics": {"name": "Admin users can view analytics", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"], "using": "is_admin_user(auth.uid())"}}, "isRLSEnabled": true}, "public.customer_interactions": {"name": "customer_interactions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "patient_id": {"name": "patient_id", "type": "uuid", "primaryKey": false, "notNull": false}, "interaction_type": {"name": "interaction_type", "type": "text", "primaryKey": false, "notNull": true}, "direction": {"name": "direction", "type": "text", "primaryKey": false, "notNull": true, "default": "'outbound'"}, "subject": {"name": "subject", "type": "text", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "outcome": {"name": "outcome", "type": "text", "primaryKey": false, "notNull": false}, "priority": {"name": "priority", "type": "interaction_priority", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'medium'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'completed'"}, "scheduled_at": {"name": "scheduled_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "follow_up_date": {"name": "follow_up_date", "type": "date", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "text[]", "primaryKey": false, "notNull": false}, "attachments": {"name": "attachments", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_customer_interactions_created_at": {"name": "idx_customer_interactions_created_at", "columns": [{"expression": "created_at", "asc": false, "nulls": "first", "opclass": "timestamptz_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_customer_interactions_patient": {"name": "idx_customer_interactions_patient", "columns": [{"expression": "patient_id", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_customer_interactions_type": {"name": "idx_customer_interactions_type", "columns": [{"expression": "interaction_type", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"customer_interactions_created_by_fkey": {"name": "customer_interactions_created_by_fkey", "tableFrom": "customer_interactions", "tableTo": "admin_users", "schemaTo": "public", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "customer_interactions_patient_id_fkey": {"name": "customer_interactions_patient_id_fkey", "tableFrom": "customer_interactions", "tableTo": "user_profiles", "schemaTo": "public", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {"Admin users can manage interactions": {"name": "Admin users can manage interactions", "as": "PERMISSIVE", "for": "ALL", "to": ["public"], "using": "is_admin_user(auth.uid())"}, "Admins can manage all customer interactions": {"name": "Admins can manage all customer interactions", "as": "PERMISSIVE", "for": "ALL", "to": ["public"]}, "Users can view their own interactions": {"name": "Users can view their own interactions", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"]}}, "isRLSEnabled": true}, "public.blog_categories": {"name": "blog_categories", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "jsonb", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "jsonb", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": false, "default": "'#3B82F6'"}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": false}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"blog_categories_slug_key": {"columns": ["slug"], "nullsNotDistinct": false, "name": "blog_categories_slug_key"}}, "checkConstraints": {}, "policies": {"Admin users can manage categories": {"name": "Admin users can manage categories", "as": "PERMISSIVE", "for": "ALL", "to": ["public"], "using": "is_admin_user(auth.uid())"}}, "isRLSEnabled": true}, "public.blog_tags": {"name": "blog_tags", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "jsonb", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": false, "default": "'#6B7280'"}, "usage_count": {"name": "usage_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"blog_tags_slug_key": {"columns": ["slug"], "nullsNotDistinct": false, "name": "blog_tags_slug_key"}}, "checkConstraints": {}, "policies": {"Admin users can manage tags": {"name": "Admin users can manage tags", "as": "PERMISSIVE", "for": "ALL", "to": ["public"], "using": "is_admin_user(auth.uid())"}, "Admins can insert blog tags": {"name": "Admins can insert blog tags", "as": "PERMISSIVE", "for": "INSERT", "to": ["public"]}}, "isRLSEnabled": true}, "public.campaign_recipients": {"name": "campaign_recipients", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "campaign_id": {"name": "campaign_id", "type": "uuid", "primaryKey": false, "notNull": false}, "patient_id": {"name": "patient_id", "type": "uuid", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "sent_at": {"name": "sent_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "delivered_at": {"name": "delivered_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "opened_at": {"name": "opened_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "clicked_at": {"name": "clicked_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "bounce_reason": {"name": "bounce_reason", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_campaign_recipients_campaign": {"name": "idx_campaign_recipients_campaign", "columns": [{"expression": "campaign_id", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_campaign_recipients_status": {"name": "idx_campaign_recipients_status", "columns": [{"expression": "status", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"campaign_recipients_campaign_id_fkey": {"name": "campaign_recipients_campaign_id_fkey", "tableFrom": "campaign_recipients", "tableTo": "marketing_campaigns", "schemaTo": "public", "columnsFrom": ["campaign_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "campaign_recipients_patient_id_fkey": {"name": "campaign_recipients_patient_id_fkey", "tableFrom": "campaign_recipients", "tableTo": "user_profiles", "schemaTo": "public", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {"Admin users can view recipients": {"name": "Admin users can view recipients", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"], "using": "is_admin_user(auth.uid())"}}, "isRLSEnabled": true}, "public.cms_media": {"name": "cms_media", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "filename": {"name": "filename", "type": "text", "primaryKey": false, "notNull": true}, "original_filename": {"name": "original_filename", "type": "text", "primaryKey": false, "notNull": true}, "storage_path": {"name": "storage_path", "type": "text", "primaryKey": false, "notNull": true}, "public_url": {"name": "public_url", "type": "text", "primaryKey": false, "notNull": false}, "mime_type": {"name": "mime_type", "type": "text", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": true}, "width": {"name": "width", "type": "integer", "primaryKey": false, "notNull": false}, "height": {"name": "height", "type": "integer", "primaryKey": false, "notNull": false}, "alt_text": {"name": "alt_text", "type": "jsonb", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "jsonb", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "text[]", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "uploaded_by": {"name": "uploaded_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_cms_media_active": {"name": "idx_cms_media_active", "columns": [{"expression": "is_active", "asc": true, "nulls": "last", "opclass": "bool_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_cms_media_mime_type": {"name": "idx_cms_media_mime_type", "columns": [{"expression": "mime_type", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"cms_media_uploaded_by_fkey": {"name": "cms_media_uploaded_by_fkey", "tableFrom": "cms_media", "tableTo": "admin_users", "schemaTo": "public", "columnsFrom": ["uploaded_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {"Admin users can manage media": {"name": "Admin users can manage media", "as": "PERMISSIVE", "for": "ALL", "to": ["public"], "using": "is_admin_user(auth.uid())"}}, "isRLSEnabled": true}, "public.cms_pages": {"name": "cms_pages", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "jsonb", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "jsonb", "primaryKey": false, "notNull": true}, "excerpt": {"name": "excerpt", "type": "jsonb", "primaryKey": false, "notNull": false}, "meta_title": {"name": "meta_title", "type": "jsonb", "primaryKey": false, "notNull": false}, "meta_description": {"name": "meta_description", "type": "jsonb", "primaryKey": false, "notNull": false}, "meta_keywords": {"name": "meta_keywords", "type": "text[]", "primaryKey": false, "notNull": false}, "featured_image_url": {"name": "featured_image_url", "type": "text", "primaryKey": false, "notNull": false}, "is_published": {"name": "is_published", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "publish_at": {"name": "publish_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "page_type": {"name": "page_type", "type": "text", "primaryKey": false, "notNull": true, "default": "'page'"}, "template": {"name": "template", "type": "text", "primaryKey": false, "notNull": false, "default": "'default'"}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "view_count": {"name": "view_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_cms_pages_created_at": {"name": "idx_cms_pages_created_at", "columns": [{"expression": "created_at", "asc": false, "nulls": "first", "opclass": "timestamptz_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_cms_pages_published": {"name": "idx_cms_pages_published", "columns": [{"expression": "is_published", "asc": true, "nulls": "last", "opclass": "bool_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_cms_pages_slug": {"name": "idx_cms_pages_slug", "columns": [{"expression": "slug", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_cms_pages_type": {"name": "idx_cms_pages_type", "columns": [{"expression": "page_type", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"cms_pages_created_by_fkey": {"name": "cms_pages_created_by_fkey", "tableFrom": "cms_pages", "tableTo": "admin_users", "schemaTo": "public", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "cms_pages_updated_by_fkey": {"name": "cms_pages_updated_by_fkey", "tableFrom": "cms_pages", "tableTo": "admin_users", "schemaTo": "public", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"cms_pages_slug_key": {"columns": ["slug"], "nullsNotDistinct": false, "name": "cms_pages_slug_key"}}, "checkConstraints": {}, "policies": {"Admin users can manage content": {"name": "Admin users can manage content", "as": "PERMISSIVE", "for": "ALL", "to": ["public"], "using": "is_admin_user(auth.uid())"}}, "isRLSEnabled": true}, "public.doctor_services": {"name": "doctor_services", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v4()"}, "doctor_id": {"name": "doctor_id", "type": "uuid", "primaryKey": false, "notNull": false}, "service_id": {"name": "service_id", "type": "uuid", "primaryKey": false, "notNull": false}, "custom_price": {"name": "custom_price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "is_available": {"name": "is_available", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"doctor_services_doctor_id_fkey": {"name": "doctor_services_doctor_id_fkey", "tableFrom": "doctor_services", "tableTo": "doctors", "schemaTo": "public", "columnsFrom": ["doctor_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "doctor_services_service_id_fkey": {"name": "doctor_services_service_id_fkey", "tableFrom": "doctor_services", "tableTo": "services", "schemaTo": "public", "columnsFrom": ["service_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"doctor_services_doctor_id_service_id_key": {"columns": ["doctor_id", "service_id"], "nullsNotDistinct": false, "name": "doctor_services_doctor_id_service_id_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": true}, "public.email_templates": {"name": "email_templates", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "subject": {"name": "subject", "type": "jsonb", "primaryKey": false, "notNull": true}, "html_content": {"name": "html_content", "type": "jsonb", "primaryKey": false, "notNull": true}, "text_content": {"name": "text_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "variables": {"name": "variables", "type": "text[]", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "usage_count": {"name": "usage_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"email_templates_created_by_fkey": {"name": "email_templates_created_by_fkey", "tableFrom": "email_templates", "tableTo": "admin_users", "schemaTo": "public", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {"Admin users can manage templates": {"name": "Admin users can manage templates", "as": "PERMISSIVE", "for": "ALL", "to": ["public"], "using": "is_admin_user(auth.uid())"}}, "isRLSEnabled": true}, "public.inventory_items": {"name": "inventory_items", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "sku": {"name": "sku", "type": "text", "primaryKey": false, "notNull": false}, "barcode": {"name": "barcode", "type": "text", "primaryKey": false, "notNull": false}, "unit_of_measure": {"name": "unit_of_measure", "type": "text", "primaryKey": false, "notNull": true}, "unit_cost": {"name": "unit_cost", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "current_stock": {"name": "current_stock", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "minimum_stock": {"name": "minimum_stock", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "maximum_stock": {"name": "maximum_stock", "type": "integer", "primaryKey": false, "notNull": false}, "reorder_point": {"name": "reorder_point", "type": "integer", "primaryKey": false, "notNull": false}, "supplier": {"name": "supplier", "type": "text", "primaryKey": false, "notNull": false}, "supplier_contact": {"name": "supplier_contact", "type": "text", "primaryKey": false, "notNull": false}, "storage_location": {"name": "storage_location", "type": "text", "primaryKey": false, "notNull": false}, "expiry_tracking": {"name": "expiry_tracking", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_inventory_items_category": {"name": "idx_inventory_items_category", "columns": [{"expression": "category", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_inventory_items_low_stock": {"name": "idx_inventory_items_low_stock", "columns": [{"expression": "current_stock", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "where": "(current_stock <= minimum_stock)", "with": {}}, "idx_inventory_items_sku": {"name": "idx_inventory_items_sku", "columns": [{"expression": "sku", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"inventory_items_created_by_fkey": {"name": "inventory_items_created_by_fkey", "tableFrom": "inventory_items", "tableTo": "admin_users", "schemaTo": "public", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"inventory_items_sku_key": {"columns": ["sku"], "nullsNotDistinct": false, "name": "inventory_items_sku_key"}, "inventory_items_barcode_key": {"columns": ["barcode"], "nullsNotDistinct": false, "name": "inventory_items_barcode_key"}}, "checkConstraints": {}, "policies": {"Admin users can manage inventory": {"name": "Admin users can manage inventory", "as": "PERMISSIVE", "for": "ALL", "to": ["public"], "using": "is_admin_user(auth.uid())"}}, "isRLSEnabled": true}, "public.inventory_transactions": {"name": "inventory_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "item_id": {"name": "item_id", "type": "uuid", "primaryKey": false, "notNull": false}, "transaction_type": {"name": "transaction_type", "type": "text", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true}, "unit_cost": {"name": "unit_cost", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "total_cost": {"name": "total_cost", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "reference_id": {"name": "reference_id", "type": "uuid", "primaryKey": false, "notNull": false}, "reference_type": {"name": "reference_type", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "batch_number": {"name": "batch_number", "type": "text", "primaryKey": false, "notNull": false}, "expiry_date": {"name": "expiry_date", "type": "date", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_inventory_transactions_created_at": {"name": "idx_inventory_transactions_created_at", "columns": [{"expression": "created_at", "asc": false, "nulls": "first", "opclass": "timestamptz_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_inventory_transactions_item": {"name": "idx_inventory_transactions_item", "columns": [{"expression": "item_id", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_inventory_transactions_type": {"name": "idx_inventory_transactions_type", "columns": [{"expression": "transaction_type", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"inventory_transactions_created_by_fkey": {"name": "inventory_transactions_created_by_fkey", "tableFrom": "inventory_transactions", "tableTo": "admin_users", "schemaTo": "public", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "inventory_transactions_item_id_fkey": {"name": "inventory_transactions_item_id_fkey", "tableFrom": "inventory_transactions", "tableTo": "inventory_items", "schemaTo": "public", "columnsFrom": ["item_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {"Admin users can manage transactions": {"name": "Admin users can manage transactions", "as": "PERMISSIVE", "for": "ALL", "to": ["public"], "using": "is_admin_user(auth.uid())"}}, "isRLSEnabled": true}, "public.kpi_metrics": {"name": "kpi_metrics", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "date": {"name": "date", "type": "date", "primaryKey": false, "notNull": true}, "metric_name": {"name": "metric_name", "type": "text", "primaryKey": false, "notNull": true}, "metric_value": {"name": "metric_value", "type": "numeric(15, 2)", "primaryKey": false, "notNull": true}, "metric_unit": {"name": "metric_unit", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_kpi_metrics_date": {"name": "idx_kpi_metrics_date", "columns": [{"expression": "date", "asc": false, "nulls": "first", "opclass": "date_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"kpi_metrics_date_metric_name_key": {"columns": ["date", "metric_name"], "nullsNotDistinct": false, "name": "kpi_metrics_date_metric_name_key"}}, "checkConstraints": {}, "policies": {"Admin users can manage kpis": {"name": "Admin users can manage kpis", "as": "PERMISSIVE", "for": "ALL", "to": ["public"], "using": "is_admin_user(auth.uid())"}}, "isRLSEnabled": true}, "public.marketing_campaigns": {"name": "marketing_campaigns", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "campaign_type": {"name": "campaign_type", "type": "text", "primaryKey": false, "notNull": true}, "target_segment": {"name": "target_segment", "type": "uuid", "primaryKey": false, "notNull": false}, "email_template_id": {"name": "email_template_id", "type": "uuid", "primaryKey": false, "notNull": false}, "subject_line": {"name": "subject_line", "type": "jsonb", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "jsonb", "primaryKey": false, "notNull": true}, "sender_name": {"name": "sender_name", "type": "text", "primaryKey": false, "notNull": false}, "sender_email": {"name": "sender_email", "type": "text", "primaryKey": false, "notNull": false}, "scheduled_at": {"name": "scheduled_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "started_at": {"name": "started_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "campaign_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'draft'"}, "total_recipients": {"name": "total_recipients", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "sent_count": {"name": "sent_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "delivered_count": {"name": "delivered_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "opened_count": {"name": "opened_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "clicked_count": {"name": "clicked_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "unsubscribed_count": {"name": "unsubscribed_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "bounced_count": {"name": "bounced_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "budget_amount": {"name": "budget_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "actual_cost": {"name": "actual_cost", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "roi_percentage": {"name": "roi_percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_marketing_campaigns_scheduled": {"name": "idx_marketing_campaigns_scheduled", "columns": [{"expression": "scheduled_at", "asc": true, "nulls": "last", "opclass": "timestamptz_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_marketing_campaigns_status": {"name": "idx_marketing_campaigns_status", "columns": [{"expression": "status", "asc": true, "nulls": "last", "opclass": "enum_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"marketing_campaigns_created_by_fkey": {"name": "marketing_campaigns_created_by_fkey", "tableFrom": "marketing_campaigns", "tableTo": "admin_users", "schemaTo": "public", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "marketing_campaigns_email_template_id_fkey": {"name": "marketing_campaigns_email_template_id_fkey", "tableFrom": "marketing_campaigns", "tableTo": "email_templates", "schemaTo": "public", "columnsFrom": ["email_template_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "marketing_campaigns_target_segment_fkey": {"name": "marketing_campaigns_target_segment_fkey", "tableFrom": "marketing_campaigns", "tableTo": "customer_segments", "schemaTo": "public", "columnsFrom": ["target_segment"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {"Admin users can manage campaigns": {"name": "Admin users can manage campaigns", "as": "PERMISSIVE", "for": "ALL", "to": ["public"], "using": "is_admin_user(auth.uid())"}}, "isRLSEnabled": true}, "public.website_metrics": {"name": "website_metrics", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "date": {"name": "date", "type": "date", "primaryKey": false, "notNull": true}, "page_path": {"name": "page_path", "type": "text", "primaryKey": false, "notNull": true}, "page_views": {"name": "page_views", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "unique_visitors": {"name": "unique_visitors", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "bounce_rate": {"name": "bounce_rate", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "avg_session_duration": {"name": "avg_session_duration", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "conversion_rate": {"name": "conversion_rate", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_website_metrics_date": {"name": "idx_website_metrics_date", "columns": [{"expression": "date", "asc": false, "nulls": "first", "opclass": "date_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"website_metrics_date_page_path_key": {"columns": ["date", "page_path"], "nullsNotDistinct": false, "name": "website_metrics_date_page_path_key"}}, "checkConstraints": {}, "policies": {"Admin users can view metrics": {"name": "Admin users can view metrics", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"], "using": "is_admin_user(auth.uid())"}}, "isRLSEnabled": true}, "public.admin_activity_logs": {"name": "admin_activity_logs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "admin_user_id": {"name": "admin_user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "performed_by": {"name": "performed_by", "type": "uuid", "primaryKey": false, "notNull": false}, "action": {"name": "action", "type": "text", "primaryKey": false, "notNull": true}, "target_type": {"name": "target_type", "type": "text", "primaryKey": false, "notNull": true}, "target_id": {"name": "target_id", "type": "uuid", "primaryKey": false, "notNull": false}, "details": {"name": "details", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_admin_activity_logs_admin_user_id": {"name": "idx_admin_activity_logs_admin_user_id", "columns": [{"expression": "admin_user_id", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_admin_activity_logs_created_at": {"name": "idx_admin_activity_logs_created_at", "columns": [{"expression": "created_at", "asc": true, "nulls": "last", "opclass": "timestamptz_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_admin_activity_logs_performed_by": {"name": "idx_admin_activity_logs_performed_by", "columns": [{"expression": "performed_by", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"admin_activity_logs_admin_user_id_fkey": {"name": "admin_activity_logs_admin_user_id_fkey", "tableFrom": "admin_activity_logs", "tableTo": "admin_users", "schemaTo": "public", "columnsFrom": ["admin_user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "admin_activity_logs_performed_by_fkey": {"name": "admin_activity_logs_performed_by_fkey", "tableFrom": "admin_activity_logs", "tableTo": "users", "schemaTo": "auth", "columnsFrom": ["performed_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {"Admin users can create activity logs": {"name": "Admin users can create activity logs", "as": "PERMISSIVE", "for": "INSERT", "to": ["public"], "withCheck": "(EXISTS ( SELECT 1\n   FROM admin_users\n  WHERE ((admin_users.id = auth.uid()) AND (admin_users.is_active = true))))"}, "Admin users can view activity logs": {"name": "Admin users can view activity logs", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"]}}, "isRLSEnabled": true}, "public.appointment_types": {"name": "appointment_types", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "duration_minutes": {"name": "duration_minutes", "type": "integer", "primaryKey": false, "notNull": true, "default": 60}, "price": {"name": "price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": false, "default": "'#3B82F6'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {"Admin can manage appointment types": {"name": "Admin can manage appointment types", "as": "PERMISSIVE", "for": "ALL", "to": ["public"], "using": "(EXISTS ( SELECT 1\n   FROM admin_users\n  WHERE ((admin_users.id = auth.uid()) AND (admin_users.is_active = true))))"}, "Anyone can view appointment types": {"name": "Anyone can view appointment types", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"]}}, "isRLSEnabled": true}, "public.time_slots": {"name": "time_slots", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "doctor_id": {"name": "doctor_id", "type": "uuid", "primaryKey": false, "notNull": false}, "date": {"name": "date", "type": "date", "primaryKey": false, "notNull": true}, "start_time": {"name": "start_time", "type": "time", "primaryKey": false, "notNull": true}, "end_time": {"name": "end_time", "type": "time", "primaryKey": false, "notNull": true}, "is_available": {"name": "is_available", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "is_recurring": {"name": "is_recurring", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "recurring_pattern": {"name": "recurring_pattern", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"time_slots_doctor_id_fkey": {"name": "time_slots_doctor_id_fkey", "tableFrom": "time_slots", "tableTo": "user_profiles", "schemaTo": "public", "columnsFrom": ["doctor_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {"Admin can manage time slots": {"name": "Admin can manage time slots", "as": "PERMISSIVE", "for": "ALL", "to": ["public"], "using": "(EXISTS ( SELECT 1\n   FROM admin_users\n  WHERE ((admin_users.id = auth.uid()) AND (admin_users.is_active = true))))"}, "Anyone can view available time slots": {"name": "Anyone can view available time slots", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"]}}, "isRLSEnabled": true}, "public.payload_migrations": {"name": "payload_migrations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "batch": {"name": "batch", "type": "numeric", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_migrations_created_at_idx": {"name": "payload_migrations_created_at_idx", "columns": [{"expression": "created_at", "asc": true, "nulls": "last", "opclass": "timestamptz_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_migrations_updated_at_idx": {"name": "payload_migrations_updated_at_idx", "columns": [{"expression": "updated_at", "asc": true, "nulls": "last", "opclass": "timestamptz_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.payload_locked_documents": {"name": "payload_locked_documents", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "global_slug": {"name": "global_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_locked_documents_created_at_idx": {"name": "payload_locked_documents_created_at_idx", "columns": [{"expression": "created_at", "asc": true, "nulls": "last", "opclass": "timestamptz_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_global_slug_idx": {"name": "payload_locked_documents_global_slug_idx", "columns": [{"expression": "global_slug", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_updated_at_idx": {"name": "payload_locked_documents_updated_at_idx", "columns": [{"expression": "updated_at", "asc": true, "nulls": "last", "opclass": "timestamptz_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.payload_locked_documents_rels": {"name": "payload_locked_documents_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "users_id": {"name": "users_id", "type": "integer", "primaryKey": false, "notNull": false}, "media_id": {"name": "media_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"payload_locked_documents_rels_media_id_idx": {"name": "payload_locked_documents_rels_media_id_idx", "columns": [{"expression": "media_id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_order_idx": {"name": "payload_locked_documents_rels_order_idx", "columns": [{"expression": "order", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_parent_idx": {"name": "payload_locked_documents_rels_parent_idx", "columns": [{"expression": "parent_id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_path_idx": {"name": "payload_locked_documents_rels_path_idx", "columns": [{"expression": "path", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_users_id_idx": {"name": "payload_locked_documents_rels_users_id_idx", "columns": [{"expression": "users_id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payload_locked_documents_rels_parent_fk": {"name": "payload_locked_documents_rels_parent_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "payload_locked_documents", "schemaTo": "public", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_users_fk": {"name": "payload_locked_documents_rels_users_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "reset_password_token": {"name": "reset_password_token", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "reset_password_expiration": {"name": "reset_password_expiration", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "salt": {"name": "salt", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "hash": {"name": "hash", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "login_attempts": {"name": "login_attempts", "type": "numeric", "primaryKey": false, "notNull": false, "default": "'0'"}, "lock_until": {"name": "lock_until", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"users_created_at_idx": {"name": "users_created_at_idx", "columns": [{"expression": "created_at", "asc": true, "nulls": "last", "opclass": "timestamptz_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_email_idx": {"name": "users_email_idx", "columns": [{"expression": "email", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "users_updated_at_idx": {"name": "users_updated_at_idx", "columns": [{"expression": "updated_at", "asc": true, "nulls": "last", "opclass": "timestamptz_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.payload_preferences": {"name": "payload_preferences", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "jsonb", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_preferences_created_at_idx": {"name": "payload_preferences_created_at_idx", "columns": [{"expression": "created_at", "asc": true, "nulls": "last", "opclass": "timestamptz_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_key_idx": {"name": "payload_preferences_key_idx", "columns": [{"expression": "key", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_updated_at_idx": {"name": "payload_preferences_updated_at_idx", "columns": [{"expression": "updated_at", "asc": true, "nulls": "last", "opclass": "timestamptz_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.payload_preferences_rels": {"name": "payload_preferences_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "users_id": {"name": "users_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"payload_preferences_rels_order_idx": {"name": "payload_preferences_rels_order_idx", "columns": [{"expression": "order", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_parent_idx": {"name": "payload_preferences_rels_parent_idx", "columns": [{"expression": "parent_id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_path_idx": {"name": "payload_preferences_rels_path_idx", "columns": [{"expression": "path", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_users_id_idx": {"name": "payload_preferences_rels_users_id_idx", "columns": [{"expression": "users_id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payload_preferences_rels_parent_fk": {"name": "payload_preferences_rels_parent_fk", "tableFrom": "payload_preferences_rels", "tableTo": "payload_preferences", "schemaTo": "public", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_preferences_rels_users_fk": {"name": "payload_preferences_rels_users_fk", "tableFrom": "payload_preferences_rels", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.media": {"name": "media", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "alt": {"name": "alt", "type": "text", "primaryKey": false, "notNull": false}, "caption": {"name": "caption", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false}, "prefix": {"name": "prefix", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false}, "thumbnail_u_r_l": {"name": "thumbnail_u_r_l", "type": "text", "primaryKey": false, "notNull": false}, "filename": {"name": "filename", "type": "text", "primaryKey": false, "notNull": false}, "mime_type": {"name": "mime_type", "type": "text", "primaryKey": false, "notNull": false}, "filesize": {"name": "filesize", "type": "integer", "primaryKey": false, "notNull": false}, "width": {"name": "width", "type": "integer", "primaryKey": false, "notNull": false}, "height": {"name": "height", "type": "integer", "primaryKey": false, "notNull": false}, "focal_x": {"name": "focal_x", "type": "integer", "primaryKey": false, "notNull": false}, "focal_y": {"name": "focal_y", "type": "integer", "primaryKey": false, "notNull": false}, "sizes_thumbnail_url": {"name": "sizes_thumbnail_url", "type": "text", "primaryKey": false, "notNull": false}, "sizes_thumbnail_width": {"name": "sizes_thumbnail_width", "type": "integer", "primaryKey": false, "notNull": false}, "sizes_thumbnail_height": {"name": "sizes_thumbnail_height", "type": "integer", "primaryKey": false, "notNull": false}, "sizes_thumbnail_mime_type": {"name": "sizes_thumbnail_mime_type", "type": "text", "primaryKey": false, "notNull": false}, "sizes_thumbnail_filesize": {"name": "sizes_thumbnail_filesize", "type": "integer", "primaryKey": false, "notNull": false}, "sizes_thumbnail_filename": {"name": "sizes_thumbnail_filename", "type": "text", "primaryKey": false, "notNull": false}, "sizes_card_url": {"name": "sizes_card_url", "type": "text", "primaryKey": false, "notNull": false}, "sizes_card_width": {"name": "sizes_card_width", "type": "integer", "primaryKey": false, "notNull": false}, "sizes_card_height": {"name": "sizes_card_height", "type": "integer", "primaryKey": false, "notNull": false}, "sizes_card_mime_type": {"name": "sizes_card_mime_type", "type": "text", "primaryKey": false, "notNull": false}, "sizes_card_filesize": {"name": "sizes_card_filesize", "type": "integer", "primaryKey": false, "notNull": false}, "sizes_card_filename": {"name": "sizes_card_filename", "type": "text", "primaryKey": false, "notNull": false}, "sizes_tablet_url": {"name": "sizes_tablet_url", "type": "text", "primaryKey": false, "notNull": false}, "sizes_tablet_width": {"name": "sizes_tablet_width", "type": "integer", "primaryKey": false, "notNull": false}, "sizes_tablet_height": {"name": "sizes_tablet_height", "type": "integer", "primaryKey": false, "notNull": false}, "sizes_tablet_mime_type": {"name": "sizes_tablet_mime_type", "type": "text", "primaryKey": false, "notNull": false}, "sizes_tablet_filesize": {"name": "sizes_tablet_filesize", "type": "integer", "primaryKey": false, "notNull": false}, "sizes_tablet_filename": {"name": "sizes_tablet_filename", "type": "text", "primaryKey": false, "notNull": false}, "sizes_desktop_url": {"name": "sizes_desktop_url", "type": "text", "primaryKey": false, "notNull": false}, "sizes_desktop_width": {"name": "sizes_desktop_width", "type": "integer", "primaryKey": false, "notNull": false}, "sizes_desktop_height": {"name": "sizes_desktop_height", "type": "integer", "primaryKey": false, "notNull": false}, "sizes_desktop_mime_type": {"name": "sizes_desktop_mime_type", "type": "text", "primaryKey": false, "notNull": false}, "sizes_desktop_filesize": {"name": "sizes_desktop_filesize", "type": "integer", "primaryKey": false, "notNull": false}, "sizes_desktop_filename": {"name": "sizes_desktop_filename", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.cms_page_categories": {"name": "cms_page_categories", "schema": "", "columns": {"page_id": {"name": "page_id", "type": "uuid", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"cms_page_categories_category_id_fkey": {"name": "cms_page_categories_category_id_fkey", "tableFrom": "cms_page_categories", "tableTo": "blog_categories", "schemaTo": "public", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "cms_page_categories_page_id_fkey": {"name": "cms_page_categories_page_id_fkey", "tableFrom": "cms_page_categories", "tableTo": "cms_pages", "schemaTo": "public", "columnsFrom": ["page_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"cms_page_categories_pkey": {"name": "cms_page_categories_pkey", "columns": ["page_id", "category_id"]}}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": true}, "public.cms_page_tags": {"name": "cms_page_tags", "schema": "", "columns": {"page_id": {"name": "page_id", "type": "uuid", "primaryKey": false, "notNull": true}, "tag_id": {"name": "tag_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"cms_page_tags_page_id_fkey": {"name": "cms_page_tags_page_id_fkey", "tableFrom": "cms_page_tags", "tableTo": "cms_pages", "schemaTo": "public", "columnsFrom": ["page_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "cms_page_tags_tag_id_fkey": {"name": "cms_page_tags_tag_id_fkey", "tableFrom": "cms_page_tags", "tableTo": "blog_tags", "schemaTo": "public", "columnsFrom": ["tag_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"cms_page_tags_pkey": {"name": "cms_page_tags_pkey", "columns": ["page_id", "tag_id"]}}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": true}, "public.segment_memberships": {"name": "segment_memberships", "schema": "", "columns": {"segment_id": {"name": "segment_id", "type": "uuid", "primaryKey": false, "notNull": true}, "patient_id": {"name": "patient_id", "type": "uuid", "primaryKey": false, "notNull": true}, "added_by": {"name": "added_by", "type": "uuid", "primaryKey": false, "notNull": false}, "added_at": {"name": "added_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"segment_memberships_added_by_fkey": {"name": "segment_memberships_added_by_fkey", "tableFrom": "segment_memberships", "tableTo": "admin_users", "schemaTo": "public", "columnsFrom": ["added_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "segment_memberships_patient_id_fkey": {"name": "segment_memberships_patient_id_fkey", "tableFrom": "segment_memberships", "tableTo": "user_profiles", "schemaTo": "public", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "segment_memberships_segment_id_fkey": {"name": "segment_memberships_segment_id_fkey", "tableFrom": "segment_memberships", "tableTo": "customer_segments", "schemaTo": "public", "columnsFrom": ["segment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"segment_memberships_pkey": {"name": "segment_memberships_pkey", "columns": ["segment_id", "patient_id"]}}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": true}}, "enums": {"public.admin_role": {"name": "admin_role", "values": ["super_admin", "admin", "manager", "staff", "content_editor", "analyst"], "schema": "public"}, "public.appointment_status": {"name": "appointment_status", "values": ["scheduled", "confirmed", "in_progress", "completed", "cancelled", "no_show"], "schema": "public"}, "public.campaign_status": {"name": "campaign_status", "values": ["draft", "scheduled", "active", "paused", "completed", "cancelled"], "schema": "public"}, "public.interaction_priority": {"name": "interaction_priority", "values": ["low", "medium", "high", "urgent"], "schema": "public"}, "public.notification_type": {"name": "notification_type", "values": ["appointment_confirmation", "appointment_reminder", "payment_confirmation", "treatment_followup", "marketing"], "schema": "public"}, "public.payment_method": {"name": "payment_method", "values": ["cash", "card", "bank_transfer", "insurance"], "schema": "public"}, "public.payment_status": {"name": "payment_status", "values": ["pending", "paid", "failed", "refunded", "partial"], "schema": "public"}, "public.treatment_difficulty": {"name": "treatment_difficulty", "values": ["beginner", "intermediate", "advanced"], "schema": "public"}, "public.user_role": {"name": "user_role", "values": ["patient", "doctor", "admin", "staff"], "schema": "public"}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {"doctors": {"columns": {"languages_spoken": {"isArray": true, "dimensions": 1, "rawType": "text"}}}, "services": {"columns": {"benefits": {"isArray": true, "dimensions": 1, "rawType": "text"}, "procedures": {"isArray": true, "dimensions": 1, "rawType": "text"}, "gallery_images": {"isArray": true, "dimensions": 1, "rawType": "text"}, "seo_keywords": {"isArray": true, "dimensions": 1, "rawType": "text"}}}, "appointments": {"columns": {"before_photos": {"isArray": true, "dimensions": 1, "rawType": "text"}, "after_photos": {"isArray": true, "dimensions": 1, "rawType": "text"}}}, "promotions": {"columns": {"applicable_services": {"isArray": true, "dimensions": 1, "rawType": "uuid"}}}, "newsletter_subscribers": {"columns": {"subscriber_tags": {"isArray": true, "dimensions": 1, "rawType": "text"}}}, "blog_posts": {"columns": {"gallery_images": {"isArray": true, "dimensions": 1, "rawType": "text"}, "tags": {"isArray": true, "dimensions": 1, "rawType": "text"}, "seo_keywords": {"isArray": true, "dimensions": 1, "rawType": "text"}}}, "customer_notes": {"columns": {"tags": {"isArray": true, "dimensions": 1, "rawType": "text"}}}, "customer_interactions": {"columns": {"tags": {"isArray": true, "dimensions": 1, "rawType": "text"}}}, "cms_media": {"columns": {"tags": {"isArray": true, "dimensions": 1, "rawType": "text"}}}, "cms_pages": {"columns": {"meta_keywords": {"isArray": true, "dimensions": 1, "rawType": "text"}}}, "email_templates": {"columns": {"variables": {"isArray": true, "dimensions": 1, "rawType": "text"}}}}}}