import { pgTable, foreignKey, unique, uuid, varchar, text, integer, numeric, jsonb, boolean, timestamp, pgPolicy, index, date, check, inet, time, serial, uniqueIndex, primaryKey, pgEnum } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"

export const adminRole = pgEnum("admin_role", ['super_admin', 'admin', 'manager', 'staff', 'content_editor', 'analyst'])
export const appointmentStatus = pgEnum("appointment_status", ['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'])
export const campaignStatus = pgEnum("campaign_status", ['draft', 'scheduled', 'active', 'paused', 'completed', 'cancelled'])
export const interactionPriority = pgEnum("interaction_priority", ['low', 'medium', 'high', 'urgent'])
export const notificationType = pgEnum("notification_type", ['appointment_confirmation', 'appointment_reminder', 'payment_confirmation', 'treatment_followup', 'marketing'])
export const paymentMethod = pgEnum("payment_method", ['cash', 'card', 'bank_transfer', 'insurance'])
export const paymentStatus = pgEnum("payment_status", ['pending', 'paid', 'failed', 'refunded', 'partial'])
export const treatmentDifficulty = pgEnum("treatment_difficulty", ['beginner', 'intermediate', 'advanced'])
export const userRole = pgEnum("user_role", ['patient', 'doctor', 'admin', 'staff'])


export const doctors = pgTable("doctors", {
	id: uuid().primaryKey().notNull(),
	licenseNumber: varchar("license_number", { length: 100 }).notNull(),
	specialization: varchar({ length: 200 }).notNull(),
	qualification: text().notNull(),
	experienceYears: integer("experience_years").default(0),
	bio: text(),
	consultationFee: numeric("consultation_fee", { precision: 10, scale:  2 }),
	languagesSpoken: text("languages_spoken").array(),
	workingHours: jsonb("working_hours"),
	isAvailable: boolean("is_available").default(true),
	rating: numeric({ precision: 3, scale:  2 }).default('5.00'),
	totalReviews: integer("total_reviews").default(0),
	imageUrl: text("image_url"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.id],
			foreignColumns: [userProfiles.id],
			name: "doctors_id_fkey"
		}).onDelete("cascade"),
	unique("doctors_license_number_key").on(table.licenseNumber),
]);

export const serviceCategories = pgTable("service_categories", {
	id: uuid().default(sql`uuid_generate_v4()`).primaryKey().notNull(),
	name: varchar({ length: 200 }).notNull(),
	description: text(),
	slug: varchar({ length: 200 }).notNull(),
	imageUrl: text("image_url"),
	sortOrder: integer("sort_order").default(0),
	isActive: boolean("is_active").default(true),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	unique("service_categories_slug_key").on(table.slug),
	pgPolicy("Service categories are public", { as: "permissive", for: "select", to: ["public"], using: sql`true` }),
]);

export const services = pgTable("services", {
	id: uuid().default(sql`uuid_generate_v4()`).primaryKey().notNull(),
	categoryId: uuid("category_id"),
	name: varchar({ length: 200 }).notNull(),
	description: text().notNull(),
	shortDescription: varchar("short_description", { length: 500 }),
	slug: varchar({ length: 200 }).notNull(),
	basePrice: numeric("base_price", { precision: 10, scale:  2 }).notNull(),
	discountedPrice: numeric("discounted_price", { precision: 10, scale:  2 }),
	durationMinutes: integer("duration_minutes").default(60).notNull(),
	difficulty: treatmentDifficulty().default('beginner'),
	preparationInstructions: text("preparation_instructions"),
	aftercareInstructions: text("aftercare_instructions"),
	contraindications: text(),
	benefits: text().array(),
	procedures: text().array(),
	imageUrl: text("image_url"),
	galleryImages: text("gallery_images").array(),
	isPopular: boolean("is_popular").default(false),
	isFeatured: boolean("is_featured").default(false),
	sortOrder: integer("sort_order").default(0),
	isActive: boolean("is_active").default(true),
	seoTitle: varchar("seo_title", { length: 200 }),
	seoDescription: varchar("seo_description", { length: 300 }),
	seoKeywords: text("seo_keywords").array(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_services_category_id").using("btree", table.categoryId.asc().nullsLast().op("uuid_ops")),
	index("idx_services_is_active").using("btree", table.isActive.asc().nullsLast().op("bool_ops")),
	index("idx_services_is_featured").using("btree", table.isFeatured.asc().nullsLast().op("bool_ops")),
	index("idx_services_is_popular").using("btree", table.isPopular.asc().nullsLast().op("bool_ops")),
	index("idx_services_slug").using("btree", table.slug.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.categoryId],
			foreignColumns: [serviceCategories.id],
			name: "services_category_id_fkey"
		}).onDelete("set null"),
	unique("services_slug_key").on(table.slug),
	pgPolicy("Services are viewable by everyone", { as: "permissive", for: "select", to: ["public"], using: sql`true` }),
]);

export const appointmentSlots = pgTable("appointment_slots", {
	id: uuid().default(sql`uuid_generate_v4()`).primaryKey().notNull(),
	doctorId: uuid("doctor_id"),
	startTime: timestamp("start_time", { withTimezone: true, mode: 'string' }).notNull(),
	endTime: timestamp("end_time", { withTimezone: true, mode: 'string' }).notNull(),
	isAvailable: boolean("is_available").default(true),
	recurringRule: jsonb("recurring_rule"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.doctorId],
			foreignColumns: [doctors.id],
			name: "appointment_slots_doctor_id_fkey"
		}).onDelete("cascade"),
]);

export const appointments = pgTable("appointments", {
	id: uuid().default(sql`uuid_generate_v4()`).primaryKey().notNull(),
	patientId: uuid("patient_id"),
	doctorId: uuid("doctor_id"),
	serviceId: uuid("service_id"),
	slotId: uuid("slot_id"),
	appointmentDate: timestamp("appointment_date", { withTimezone: true, mode: 'string' }).notNull(),
	durationMinutes: integer("duration_minutes").default(60).notNull(),
	status: appointmentStatus().default('scheduled'),
	totalAmount: numeric("total_amount", { precision: 10, scale:  2 }).notNull(),
	depositAmount: numeric("deposit_amount", { precision: 10, scale:  2 }).default('0'),
	patientNotes: text("patient_notes"),
	doctorNotes: text("doctor_notes"),
	treatmentPlan: text("treatment_plan"),
	beforePhotos: text("before_photos").array(),
	afterPhotos: text("after_photos").array(),
	prescription: text(),
	nextAppointmentRecommended: boolean("next_appointment_recommended").default(false),
	followupDate: date("followup_date"),
	confirmationSentAt: timestamp("confirmation_sent_at", { withTimezone: true, mode: 'string' }),
	reminderSentAt: timestamp("reminder_sent_at", { withTimezone: true, mode: 'string' }),
	checkedInAt: timestamp("checked_in_at", { withTimezone: true, mode: 'string' }),
	completedAt: timestamp("completed_at", { withTimezone: true, mode: 'string' }),
	cancelledAt: timestamp("cancelled_at", { withTimezone: true, mode: 'string' }),
	cancellationReason: text("cancellation_reason"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	appointmentTypeId: uuid("appointment_type_id"),
	patientPhone: text("patient_phone"),
	patientEmail: text("patient_email"),
}, (table) => [
	index("idx_appointments_created_at").using("btree", table.createdAt.asc().nullsLast().op("timestamptz_ops")),
	index("idx_appointments_date").using("btree", table.appointmentDate.asc().nullsLast().op("timestamptz_ops")),
	index("idx_appointments_doctor_id").using("btree", table.doctorId.asc().nullsLast().op("uuid_ops")),
	index("idx_appointments_patient_id").using("btree", table.patientId.asc().nullsLast().op("uuid_ops")),
	index("idx_appointments_service_id").using("btree", table.serviceId.asc().nullsLast().op("uuid_ops")),
	index("idx_appointments_status").using("btree", table.status.asc().nullsLast().op("enum_ops")),
	foreignKey({
			columns: [table.appointmentTypeId],
			foreignColumns: [appointmentTypes.id],
			name: "appointments_appointment_type_id_fkey"
		}),
	foreignKey({
			columns: [table.doctorId],
			foreignColumns: [doctors.id],
			name: "appointments_doctor_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.patientId],
			foreignColumns: [userProfiles.id],
			name: "appointments_patient_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.serviceId],
			foreignColumns: [services.id],
			name: "appointments_service_id_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.slotId],
			foreignColumns: [appointmentSlots.id],
			name: "appointments_slot_id_fkey"
		}).onDelete("set null"),
	pgPolicy("Admin can manage all appointments", { as: "permissive", for: "all", to: ["public"], using: sql`(EXISTS ( SELECT 1
   FROM admin_users
  WHERE ((admin_users.id = auth.uid()) AND (admin_users.is_active = true))))` }),
	pgPolicy("Admins can manage all appointments", { as: "permissive", for: "all", to: ["public"] }),
	pgPolicy("Patients can create appointments", { as: "permissive", for: "insert", to: ["public"] }),
	pgPolicy("Patients can view own appointments", { as: "permissive", for: "select", to: ["public"] }),
	pgPolicy("Patients can view their own appointments", { as: "permissive", for: "select", to: ["public"] }),
	pgPolicy("Users can create appointments for themselves", { as: "permissive", for: "insert", to: ["public"] }),
	pgPolicy("Users can view their own appointments", { as: "permissive", for: "select", to: ["public"] }),
]);

export const invoices = pgTable("invoices", {
	id: uuid().default(sql`uuid_generate_v4()`).primaryKey().notNull(),
	appointmentId: uuid("appointment_id"),
	paymentId: uuid("payment_id"),
	invoiceNumber: varchar("invoice_number", { length: 100 }).notNull(),
	issueDate: date("issue_date").default(sql`CURRENT_DATE`).notNull(),
	dueDate: date("due_date"),
	subtotal: numeric({ precision: 10, scale:  2 }).notNull(),
	taxAmount: numeric("tax_amount", { precision: 10, scale:  2 }).default('0'),
	discountAmount: numeric("discount_amount", { precision: 10, scale:  2 }).default('0'),
	totalAmount: numeric("total_amount", { precision: 10, scale:  2 }).notNull(),
	currency: varchar({ length: 3 }).default('THB'),
	notes: text(),
	isPaid: boolean("is_paid").default(false),
	pdfUrl: text("pdf_url"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.appointmentId],
			foreignColumns: [appointments.id],
			name: "invoices_appointment_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.paymentId],
			foreignColumns: [payments.id],
			name: "invoices_payment_id_fkey"
		}).onDelete("set null"),
	unique("invoices_invoice_number_key").on(table.invoiceNumber),
	pgPolicy("Admins can insert invoices", { as: "permissive", for: "insert", to: ["public"], withCheck: sql`is_current_user_admin()`  }),
]);

export const payments = pgTable("payments", {
	id: uuid().default(sql`uuid_generate_v4()`).primaryKey().notNull(),
	appointmentId: uuid("appointment_id"),
	patientId: uuid("patient_id"),
	amount: numeric({ precision: 10, scale:  2 }).notNull(),
	paymentMethod: paymentMethod("payment_method").notNull(),
	paymentStatus: paymentStatus("payment_status").default('pending'),
	transactionId: varchar("transaction_id", { length: 200 }),
	stripePaymentIntentId: varchar("stripe_payment_intent_id", { length: 200 }),
	paymentDate: timestamp("payment_date", { withTimezone: true, mode: 'string' }),
	refundAmount: numeric("refund_amount", { precision: 10, scale:  2 }).default('0'),
	refundDate: timestamp("refund_date", { withTimezone: true, mode: 'string' }),
	refundReason: text("refund_reason"),
	receiptUrl: text("receipt_url"),
	metadata: jsonb(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.appointmentId],
			foreignColumns: [appointments.id],
			name: "payments_appointment_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.patientId],
			foreignColumns: [userProfiles.id],
			name: "payments_patient_id_fkey"
		}).onDelete("cascade"),
	unique("payments_transaction_id_key").on(table.transactionId),
	pgPolicy("Admins can insert payments", { as: "permissive", for: "insert", to: ["public"], withCheck: sql`is_current_user_admin()`  }),
	pgPolicy("Admins can view all payments", { as: "permissive", for: "all", to: ["public"] }),
	pgPolicy("Users can view their own payments", { as: "permissive", for: "select", to: ["public"] }),
]);

export const clinicSettings = pgTable("clinic_settings", {
	id: uuid().default(sql`uuid_generate_v4()`).primaryKey().notNull(),
	key: varchar({ length: 100 }).notNull(),
	value: text(),
	description: text(),
	dataType: varchar("data_type", { length: 20 }).default('string'),
	isPublic: boolean("is_public").default(false),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedBy: uuid("updated_by"),
}, (table) => [
	foreignKey({
			columns: [table.updatedBy],
			foreignColumns: [userProfiles.id],
			name: "clinic_settings_updated_by_fkey"
		}),
	unique("clinic_settings_key_key").on(table.key),
]);

export const promotions = pgTable("promotions", {
	id: uuid().default(sql`uuid_generate_v4()`).primaryKey().notNull(),
	title: varchar({ length: 200 }).notNull(),
	description: text().notNull(),
	discountType: varchar("discount_type", { length: 20 }).notNull(),
	discountValue: numeric("discount_value", { precision: 10, scale:  2 }).notNull(),
	minPurchaseAmount: numeric("min_purchase_amount", { precision: 10, scale:  2 }),
	maxDiscountAmount: numeric("max_discount_amount", { precision: 10, scale:  2 }),
	promoCode: varchar("promo_code", { length: 50 }),
	startDate: timestamp("start_date", { withTimezone: true, mode: 'string' }).notNull(),
	endDate: timestamp("end_date", { withTimezone: true, mode: 'string' }).notNull(),
	usageLimit: integer("usage_limit"),
	usageCount: integer("usage_count").default(0),
	applicableServices: uuid("applicable_services").array(),
	isActive: boolean("is_active").default(true),
	isFeatured: boolean("is_featured").default(false),
	imageUrl: text("image_url"),
	termsConditions: text("terms_conditions"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	unique("promotions_promo_code_key").on(table.promoCode),
	check("promotions_discount_type_check", sql`(discount_type)::text = ANY ((ARRAY['percentage'::character varying, 'fixed_amount'::character varying])::text[])`),
]);

export const newsletterSubscribers = pgTable("newsletter_subscribers", {
	id: uuid().default(sql`uuid_generate_v4()`).primaryKey().notNull(),
	email: varchar({ length: 255 }).notNull(),
	firstName: varchar("first_name", { length: 100 }),
	lastName: varchar("last_name", { length: 100 }),
	language: varchar({ length: 10 }).default('th'),
	subscribedAt: timestamp("subscribed_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	unsubscribedAt: timestamp("unsubscribed_at", { withTimezone: true, mode: 'string' }),
	isActive: boolean("is_active").default(true),
	subscriberTags: text("subscriber_tags").array(),
	source: varchar({ length: 100 }),
	preferences: jsonb(),
}, (table) => [
	unique("newsletter_subscribers_email_key").on(table.email),
]);

export const reviews = pgTable("reviews", {
	id: uuid().default(sql`uuid_generate_v4()`).primaryKey().notNull(),
	patientId: uuid("patient_id"),
	doctorId: uuid("doctor_id"),
	serviceId: uuid("service_id"),
	appointmentId: uuid("appointment_id"),
	rating: integer().notNull(),
	title: varchar({ length: 200 }),
	comment: text(),
	isAnonymous: boolean("is_anonymous").default(false),
	isApproved: boolean("is_approved").default(false),
	isFeatured: boolean("is_featured").default(false),
	helpfulCount: integer("helpful_count").default(0),
	beforePhotoUrl: text("before_photo_url"),
	afterPhotoUrl: text("after_photo_url"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.appointmentId],
			foreignColumns: [appointments.id],
			name: "reviews_appointment_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.doctorId],
			foreignColumns: [doctors.id],
			name: "reviews_doctor_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.patientId],
			foreignColumns: [userProfiles.id],
			name: "reviews_patient_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.serviceId],
			foreignColumns: [services.id],
			name: "reviews_service_id_fkey"
		}).onDelete("cascade"),
	unique("reviews_patient_id_appointment_id_key").on(table.patientId, table.appointmentId),
	pgPolicy("Admins can manage all reviews", { as: "permissive", for: "all", to: ["public"], using: sql`(EXISTS ( SELECT 1
   FROM admin_users
  WHERE ((admin_users.id = auth.uid()) AND (admin_users.is_active = true))))` }),
	pgPolicy("Patients can create own reviews", { as: "permissive", for: "insert", to: ["public"] }),
	pgPolicy("Patients can view approved reviews", { as: "permissive", for: "select", to: ["public"] }),
	pgPolicy("Users can create their own reviews", { as: "permissive", for: "insert", to: ["public"] }),
	pgPolicy("Users can update their own reviews", { as: "permissive", for: "update", to: ["public"] }),
	pgPolicy("Users can view their own reviews", { as: "permissive", for: "select", to: ["public"] }),
	check("reviews_rating_check", sql`(rating >= 1) AND (rating <= 5)`),
]);

export const blogPosts = pgTable("blog_posts", {
	id: uuid().default(sql`uuid_generate_v4()`).primaryKey().notNull(),
	authorId: uuid("author_id"),
	title: varchar({ length: 300 }).notNull(),
	slug: varchar({ length: 300 }).notNull(),
	excerpt: text(),
	content: text().notNull(),
	featuredImage: text("featured_image"),
	galleryImages: text("gallery_images").array(),
	category: varchar({ length: 100 }),
	tags: text().array(),
	difficulty: treatmentDifficulty().default('beginner'),
	readTimeMinutes: integer("read_time_minutes").default(5),
	viewCount: integer("view_count").default(0),
	likeCount: integer("like_count").default(0),
	isPublished: boolean("is_published").default(false),
	isFeatured: boolean("is_featured").default(false),
	publishedAt: timestamp("published_at", { withTimezone: true, mode: 'string' }),
	seoTitle: varchar("seo_title", { length: 200 }),
	seoDescription: varchar("seo_description", { length: 300 }),
	seoKeywords: text("seo_keywords").array(),
	language: varchar({ length: 10 }).default('th'),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.authorId],
			foreignColumns: [userProfiles.id],
			name: "blog_posts_author_id_fkey"
		}).onDelete("set null"),
	unique("blog_posts_slug_key").on(table.slug),
	pgPolicy("Published blog posts are viewable by everyone", { as: "permissive", for: "select", to: ["public"], using: sql`(is_published = true)` }),
]);

export const notifications = pgTable("notifications", {
	id: uuid().default(sql`uuid_generate_v4()`).primaryKey().notNull(),
	recipientId: uuid("recipient_id"),
	type: notificationType().notNull(),
	channel: varchar({ length: 20 }).notNull(),
	subject: varchar({ length: 200 }),
	content: text().notNull(),
	templateId: varchar("template_id", { length: 100 }),
	appointmentId: uuid("appointment_id"),
	sentAt: timestamp("sent_at", { withTimezone: true, mode: 'string' }),
	deliveryStatus: varchar("delivery_status", { length: 20 }).default('pending'),
	errorMessage: text("error_message"),
	metadata: jsonb(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.appointmentId],
			foreignColumns: [appointments.id],
			name: "notifications_appointment_id_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.recipientId],
			foreignColumns: [userProfiles.id],
			name: "notifications_recipient_id_fkey"
		}).onDelete("cascade"),
	check("notifications_channel_check", sql`(channel)::text = ANY ((ARRAY['email'::character varying, 'sms'::character varying, 'push'::character varying, 'in_app'::character varying])::text[])`),
]);

export const promotionUsage = pgTable("promotion_usage", {
	id: uuid().default(sql`uuid_generate_v4()`).primaryKey().notNull(),
	promotionId: uuid("promotion_id"),
	patientId: uuid("patient_id"),
	appointmentId: uuid("appointment_id"),
	discountAmount: numeric("discount_amount", { precision: 10, scale:  2 }).notNull(),
	usedAt: timestamp("used_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.appointmentId],
			foreignColumns: [appointments.id],
			name: "promotion_usage_appointment_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.patientId],
			foreignColumns: [userProfiles.id],
			name: "promotion_usage_patient_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.promotionId],
			foreignColumns: [promotions.id],
			name: "promotion_usage_promotion_id_fkey"
		}).onDelete("cascade"),
]);

export const customerSegments = pgTable("customer_segments", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	name: text().notNull(),
	description: text(),
	criteria: jsonb().notNull(),
	color: text().default('#3B82F6'),
	icon: text(),
	isDynamic: boolean("is_dynamic").default(true),
	patientCount: integer("patient_count").default(0),
	lastUpdatedAt: timestamp("last_updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	createdBy: uuid("created_by"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [adminUsers.id],
			name: "customer_segments_created_by_fkey"
		}),
	pgPolicy("Admin users can manage segments", { as: "permissive", for: "all", to: ["public"], using: sql`is_admin_user(auth.uid())` }),
]);

export const adminProfiles = pgTable("admin_profiles", {
	id: uuid().primaryKey().notNull(),
	firstName: text("first_name").notNull(),
	lastName: text("last_name").notNull(),
	email: text().notNull(),
	phone: text(),
	avatarUrl: text("avatar_url"),
	bio: text(),
	preferences: jsonb().default({}),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.id],
			foreignColumns: [adminUsers.id],
			name: "admin_profiles_id_fkey"
		}).onDelete("cascade"),
	unique("admin_profiles_email_key").on(table.email),
	pgPolicy("Admin users can view their own profile", { as: "permissive", for: "all", to: ["public"], using: sql`(is_admin_user(auth.uid()) AND ((get_admin_role(auth.uid()) = ANY (ARRAY['super_admin'::admin_role, 'admin'::admin_role])) OR (auth.uid() = id)))` }),
]);

export const userProfiles = pgTable("user_profiles", {
	id: uuid().primaryKey().notNull(),
	role: userRole().default('patient').notNull(),
	firstName: varchar("first_name", { length: 100 }).notNull(),
	lastName: varchar("last_name", { length: 100 }).notNull(),
	email: varchar({ length: 255 }).notNull(),
	phone: varchar({ length: 20 }),
	dateOfBirth: date("date_of_birth"),
	gender: varchar({ length: 20 }),
	address: text(),
	city: varchar({ length: 100 }),
	country: varchar({ length: 100 }).default('Thailand'),
	emergencyContactName: varchar("emergency_contact_name", { length: 200 }),
	emergencyContactPhone: varchar("emergency_contact_phone", { length: 20 }),
	medicalHistory: text("medical_history"),
	allergies: text(),
	currentMedications: text("current_medications"),
	preferredLanguage: varchar("preferred_language", { length: 10 }).default('th'),
	marketingConsent: boolean("marketing_consent").default(false),
	privacyConsent: boolean("privacy_consent").default(true).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	lastLogin: timestamp("last_login", { withTimezone: true, mode: 'string' }),
	isActive: boolean("is_active").default(true),
}, (table) => [
	index("idx_user_profiles_created_at").using("btree", table.createdAt.asc().nullsLast().op("timestamptz_ops")),
	index("idx_user_profiles_email").using("btree", table.email.asc().nullsLast().op("text_ops")),
	index("idx_user_profiles_phone").using("btree", table.phone.asc().nullsLast().op("text_ops")),
	index("idx_user_profiles_role").using("btree", table.role.asc().nullsLast().op("enum_ops")),
	foreignKey({
			columns: [table.id],
			foreignColumns: [users.id],
			name: "user_profiles_id_fkey"
		}).onDelete("cascade"),
	unique("user_profiles_email_key").on(table.email),
	pgPolicy("Admins can view all user profiles", { as: "permissive", for: "all", to: ["public"], using: sql`(EXISTS ( SELECT 1
   FROM admin_users
  WHERE ((admin_users.id = auth.uid()) AND (admin_users.is_active = true))))` }),
	pgPolicy("Users can create own profile", { as: "permissive", for: "insert", to: ["public"] }),
	pgPolicy("Users can update own profile", { as: "permissive", for: "update", to: ["public"] }),
	pgPolicy("Users can update their own profile", { as: "permissive", for: "update", to: ["public"] }),
	pgPolicy("Users can view own profile", { as: "permissive", for: "select", to: ["public"] }),
	pgPolicy("Users can view their own profile", { as: "permissive", for: "select", to: ["public"] }),
]);

export const customerNotes = pgTable("customer_notes", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	patientId: uuid("patient_id"),
	noteType: text("note_type").default('general'),
	title: text(),
	content: text().notNull(),
	isPrivate: boolean("is_private").default(false),
	isImportant: boolean("is_important").default(false),
	tags: text().array(),
	createdBy: uuid("created_by"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_customer_notes_patient").using("btree", table.patientId.asc().nullsLast().op("uuid_ops")),
	index("idx_customer_notes_type").using("btree", table.noteType.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [adminUsers.id],
			name: "customer_notes_created_by_fkey"
		}),
	foreignKey({
			columns: [table.patientId],
			foreignColumns: [userProfiles.id],
			name: "customer_notes_patient_id_fkey"
		}).onDelete("cascade"),
	pgPolicy("Admin users can manage customer notes", { as: "permissive", for: "all", to: ["public"], using: sql`(is_admin_user(auth.uid()) AND ((NOT is_private) OR (created_by = auth.uid()) OR (get_admin_role(auth.uid()) = ANY (ARRAY['super_admin'::admin_role, 'admin'::admin_role]))))` }),
	pgPolicy("Admins can manage all customer notes", { as: "permissive", for: "all", to: ["public"] }),
	pgPolicy("Users can view notes about themselves", { as: "permissive", for: "select", to: ["public"] }),
]);

export const adminUsers = pgTable("admin_users", {
	id: uuid().primaryKey().notNull(),
	role: adminRole().default('staff').notNull(),
	permissions: jsonb().default({}),
	department: text(),
	employeeId: text("employee_id"),
	hireDate: date("hire_date"),
	isActive: boolean("is_active").default(true),
	lastLoginAt: timestamp("last_login_at", { withTimezone: true, mode: 'string' }),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_admin_users_department").using("btree", table.department.asc().nullsLast().op("text_ops")),
	index("idx_admin_users_is_active").using("btree", table.isActive.asc().nullsLast().op("bool_ops")),
	index("idx_admin_users_role").using("btree", table.role.asc().nullsLast().op("enum_ops")),
	foreignKey({
			columns: [table.id],
			foreignColumns: [users.id],
			name: "admin_users_id_fkey"
		}).onDelete("cascade"),
	unique("admin_users_employee_id_key").on(table.employeeId),
	pgPolicy("Admins can delete admin users", { as: "permissive", for: "delete", to: ["public"], using: sql`is_current_user_admin()` }),
	pgPolicy("Admins can insert admin users", { as: "permissive", for: "insert", to: ["public"] }),
	pgPolicy("Admins can update admin users", { as: "permissive", for: "update", to: ["public"] }),
	pgPolicy("Admins can view admin users", { as: "permissive", for: "select", to: ["public"] }),
	pgPolicy("Super admins and managers can manage admin users", { as: "permissive", for: "all", to: ["public"] }),
]);

export const analyticsEvents = pgTable("analytics_events", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	eventType: text("event_type").notNull(),
	eventCategory: text("event_category").notNull(),
	eventData: jsonb("event_data").notNull(),
	patientId: uuid("patient_id"),
	sessionId: text("session_id"),
	pageUrl: text("page_url"),
	referrerUrl: text("referrer_url"),
	ipAddress: inet("ip_address"),
	userAgent: text("user_agent"),
	deviceType: text("device_type"),
	browser: text(),
	os: text(),
	country: text(),
	city: text(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_analytics_events_category").using("btree", table.eventCategory.asc().nullsLast().op("text_ops")),
	index("idx_analytics_events_created_at").using("btree", table.createdAt.desc().nullsFirst().op("timestamptz_ops")),
	index("idx_analytics_events_patient").using("btree", table.patientId.asc().nullsLast().op("uuid_ops")),
	index("idx_analytics_events_type").using("btree", table.eventType.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.patientId],
			foreignColumns: [userProfiles.id],
			name: "analytics_events_patient_id_fkey"
		}),
	pgPolicy("Admin users can view analytics", { as: "permissive", for: "select", to: ["public"], using: sql`is_admin_user(auth.uid())` }),
]);

export const customerInteractions = pgTable("customer_interactions", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	patientId: uuid("patient_id"),
	interactionType: text("interaction_type").notNull(),
	direction: text().default('outbound').notNull(),
	subject: text(),
	content: text(),
	outcome: text(),
	priority: interactionPriority().default('medium'),
	status: text().default('completed'),
	scheduledAt: timestamp("scheduled_at", { withTimezone: true, mode: 'string' }),
	completedAt: timestamp("completed_at", { withTimezone: true, mode: 'string' }),
	followUpDate: date("follow_up_date"),
	tags: text().array(),
	attachments: jsonb().default([]),
	createdBy: uuid("created_by"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_customer_interactions_created_at").using("btree", table.createdAt.desc().nullsFirst().op("timestamptz_ops")),
	index("idx_customer_interactions_patient").using("btree", table.patientId.asc().nullsLast().op("uuid_ops")),
	index("idx_customer_interactions_type").using("btree", table.interactionType.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [adminUsers.id],
			name: "customer_interactions_created_by_fkey"
		}),
	foreignKey({
			columns: [table.patientId],
			foreignColumns: [userProfiles.id],
			name: "customer_interactions_patient_id_fkey"
		}).onDelete("cascade"),
	pgPolicy("Admin users can manage interactions", { as: "permissive", for: "all", to: ["public"], using: sql`is_admin_user(auth.uid())` }),
	pgPolicy("Admins can manage all customer interactions", { as: "permissive", for: "all", to: ["public"] }),
	pgPolicy("Users can view their own interactions", { as: "permissive", for: "select", to: ["public"] }),
]);

export const blogCategories = pgTable("blog_categories", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	name: jsonb().notNull(),
	slug: text().notNull(),
	description: jsonb(),
	color: text().default('#3B82F6'),
	icon: text(),
	sortOrder: integer("sort_order").default(0),
	isActive: boolean("is_active").default(true),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	unique("blog_categories_slug_key").on(table.slug),
	pgPolicy("Admin users can manage categories", { as: "permissive", for: "all", to: ["public"], using: sql`is_admin_user(auth.uid())` }),
]);

export const blogTags = pgTable("blog_tags", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	name: jsonb().notNull(),
	slug: text().notNull(),
	color: text().default('#6B7280'),
	usageCount: integer("usage_count").default(0),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	unique("blog_tags_slug_key").on(table.slug),
	pgPolicy("Admin users can manage tags", { as: "permissive", for: "all", to: ["public"], using: sql`is_admin_user(auth.uid())` }),
	pgPolicy("Admins can insert blog tags", { as: "permissive", for: "insert", to: ["public"] }),
]);

export const campaignRecipients = pgTable("campaign_recipients", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	campaignId: uuid("campaign_id"),
	patientId: uuid("patient_id"),
	email: text().notNull(),
	status: text().default('pending'),
	sentAt: timestamp("sent_at", { withTimezone: true, mode: 'string' }),
	deliveredAt: timestamp("delivered_at", { withTimezone: true, mode: 'string' }),
	openedAt: timestamp("opened_at", { withTimezone: true, mode: 'string' }),
	clickedAt: timestamp("clicked_at", { withTimezone: true, mode: 'string' }),
	bounceReason: text("bounce_reason"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_campaign_recipients_campaign").using("btree", table.campaignId.asc().nullsLast().op("uuid_ops")),
	index("idx_campaign_recipients_status").using("btree", table.status.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.campaignId],
			foreignColumns: [marketingCampaigns.id],
			name: "campaign_recipients_campaign_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.patientId],
			foreignColumns: [userProfiles.id],
			name: "campaign_recipients_patient_id_fkey"
		}).onDelete("cascade"),
	pgPolicy("Admin users can view recipients", { as: "permissive", for: "select", to: ["public"], using: sql`is_admin_user(auth.uid())` }),
]);

export const cmsMedia = pgTable("cms_media", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	filename: text().notNull(),
	originalFilename: text("original_filename").notNull(),
	storagePath: text("storage_path").notNull(),
	publicUrl: text("public_url"),
	mimeType: text("mime_type").notNull(),
	fileSize: integer("file_size").notNull(),
	width: integer(),
	height: integer(),
	altText: jsonb("alt_text"),
	description: jsonb(),
	tags: text().array(),
	isActive: boolean("is_active").default(true),
	uploadedBy: uuid("uploaded_by"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_cms_media_active").using("btree", table.isActive.asc().nullsLast().op("bool_ops")),
	index("idx_cms_media_mime_type").using("btree", table.mimeType.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.uploadedBy],
			foreignColumns: [adminUsers.id],
			name: "cms_media_uploaded_by_fkey"
		}),
	pgPolicy("Admin users can manage media", { as: "permissive", for: "all", to: ["public"], using: sql`is_admin_user(auth.uid())` }),
]);

export const cmsPages = pgTable("cms_pages", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	slug: text().notNull(),
	title: jsonb().notNull(),
	content: jsonb().notNull(),
	excerpt: jsonb(),
	metaTitle: jsonb("meta_title"),
	metaDescription: jsonb("meta_description"),
	metaKeywords: text("meta_keywords").array(),
	featuredImageUrl: text("featured_image_url"),
	isPublished: boolean("is_published").default(false),
	publishAt: timestamp("publish_at", { withTimezone: true, mode: 'string' }),
	pageType: text("page_type").default('page').notNull(),
	template: text().default('default'),
	sortOrder: integer("sort_order").default(0),
	viewCount: integer("view_count").default(0),
	createdBy: uuid("created_by"),
	updatedBy: uuid("updated_by"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_cms_pages_created_at").using("btree", table.createdAt.desc().nullsFirst().op("timestamptz_ops")),
	index("idx_cms_pages_published").using("btree", table.isPublished.asc().nullsLast().op("bool_ops")),
	index("idx_cms_pages_slug").using("btree", table.slug.asc().nullsLast().op("text_ops")),
	index("idx_cms_pages_type").using("btree", table.pageType.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [adminUsers.id],
			name: "cms_pages_created_by_fkey"
		}),
	foreignKey({
			columns: [table.updatedBy],
			foreignColumns: [adminUsers.id],
			name: "cms_pages_updated_by_fkey"
		}),
	unique("cms_pages_slug_key").on(table.slug),
	pgPolicy("Admin users can manage content", { as: "permissive", for: "all", to: ["public"], using: sql`is_admin_user(auth.uid())` }),
]);

export const doctorServices = pgTable("doctor_services", {
	id: uuid().default(sql`uuid_generate_v4()`).primaryKey().notNull(),
	doctorId: uuid("doctor_id"),
	serviceId: uuid("service_id"),
	customPrice: numeric("custom_price", { precision: 10, scale:  2 }),
	isAvailable: boolean("is_available").default(true),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.doctorId],
			foreignColumns: [doctors.id],
			name: "doctor_services_doctor_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.serviceId],
			foreignColumns: [services.id],
			name: "doctor_services_service_id_fkey"
		}).onDelete("cascade"),
	unique("doctor_services_doctor_id_service_id_key").on(table.doctorId, table.serviceId),
]);

export const emailTemplates = pgTable("email_templates", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	name: text().notNull(),
	category: text().notNull(),
	subject: jsonb().notNull(),
	htmlContent: jsonb("html_content").notNull(),
	textContent: jsonb("text_content"),
	variables: text().array(),
	isActive: boolean("is_active").default(true),
	usageCount: integer("usage_count").default(0),
	createdBy: uuid("created_by"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [adminUsers.id],
			name: "email_templates_created_by_fkey"
		}),
	pgPolicy("Admin users can manage templates", { as: "permissive", for: "all", to: ["public"], using: sql`is_admin_user(auth.uid())` }),
]);

export const inventoryItems = pgTable("inventory_items", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	name: text().notNull(),
	description: text(),
	category: text().notNull(),
	sku: text(),
	barcode: text(),
	unitOfMeasure: text("unit_of_measure").notNull(),
	unitCost: numeric("unit_cost", { precision: 10, scale:  2 }),
	currentStock: integer("current_stock").default(0),
	minimumStock: integer("minimum_stock").default(0),
	maximumStock: integer("maximum_stock"),
	reorderPoint: integer("reorder_point"),
	supplier: text(),
	supplierContact: text("supplier_contact"),
	storageLocation: text("storage_location"),
	expiryTracking: boolean("expiry_tracking").default(false),
	isActive: boolean("is_active").default(true),
	createdBy: uuid("created_by"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_inventory_items_category").using("btree", table.category.asc().nullsLast().op("text_ops")),
	index("idx_inventory_items_low_stock").using("btree", table.currentStock.asc().nullsLast().op("int4_ops")).where(sql`(current_stock <= minimum_stock)`),
	index("idx_inventory_items_sku").using("btree", table.sku.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [adminUsers.id],
			name: "inventory_items_created_by_fkey"
		}),
	unique("inventory_items_sku_key").on(table.sku),
	unique("inventory_items_barcode_key").on(table.barcode),
	pgPolicy("Admin users can manage inventory", { as: "permissive", for: "all", to: ["public"], using: sql`is_admin_user(auth.uid())` }),
]);

export const inventoryTransactions = pgTable("inventory_transactions", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	itemId: uuid("item_id"),
	transactionType: text("transaction_type").notNull(),
	quantity: integer().notNull(),
	unitCost: numeric("unit_cost", { precision: 10, scale:  2 }),
	totalCost: numeric("total_cost", { precision: 10, scale:  2 }),
	referenceId: uuid("reference_id"),
	referenceType: text("reference_type"),
	notes: text(),
	batchNumber: text("batch_number"),
	expiryDate: date("expiry_date"),
	createdBy: uuid("created_by"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_inventory_transactions_created_at").using("btree", table.createdAt.desc().nullsFirst().op("timestamptz_ops")),
	index("idx_inventory_transactions_item").using("btree", table.itemId.asc().nullsLast().op("uuid_ops")),
	index("idx_inventory_transactions_type").using("btree", table.transactionType.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [adminUsers.id],
			name: "inventory_transactions_created_by_fkey"
		}),
	foreignKey({
			columns: [table.itemId],
			foreignColumns: [inventoryItems.id],
			name: "inventory_transactions_item_id_fkey"
		}).onDelete("cascade"),
	pgPolicy("Admin users can manage transactions", { as: "permissive", for: "all", to: ["public"], using: sql`is_admin_user(auth.uid())` }),
]);

export const kpiMetrics = pgTable("kpi_metrics", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	date: date().notNull(),
	metricName: text("metric_name").notNull(),
	metricValue: numeric("metric_value", { precision: 15, scale:  2 }).notNull(),
	metricUnit: text("metric_unit"),
	category: text().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_kpi_metrics_date").using("btree", table.date.desc().nullsFirst().op("date_ops")),
	unique("kpi_metrics_date_metric_name_key").on(table.date, table.metricName),
	pgPolicy("Admin users can manage kpis", { as: "permissive", for: "all", to: ["public"], using: sql`is_admin_user(auth.uid())` }),
]);

export const marketingCampaigns = pgTable("marketing_campaigns", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	name: text().notNull(),
	description: text(),
	campaignType: text("campaign_type").notNull(),
	targetSegment: uuid("target_segment"),
	emailTemplateId: uuid("email_template_id"),
	subjectLine: jsonb("subject_line"),
	content: jsonb().notNull(),
	senderName: text("sender_name"),
	senderEmail: text("sender_email"),
	scheduledAt: timestamp("scheduled_at", { withTimezone: true, mode: 'string' }),
	startedAt: timestamp("started_at", { withTimezone: true, mode: 'string' }),
	completedAt: timestamp("completed_at", { withTimezone: true, mode: 'string' }),
	status: campaignStatus().default('draft'),
	totalRecipients: integer("total_recipients").default(0),
	sentCount: integer("sent_count").default(0),
	deliveredCount: integer("delivered_count").default(0),
	openedCount: integer("opened_count").default(0),
	clickedCount: integer("clicked_count").default(0),
	unsubscribedCount: integer("unsubscribed_count").default(0),
	bouncedCount: integer("bounced_count").default(0),
	budgetAmount: numeric("budget_amount", { precision: 10, scale:  2 }),
	actualCost: numeric("actual_cost", { precision: 10, scale:  2 }),
	roiPercentage: numeric("roi_percentage", { precision: 5, scale:  2 }),
	createdBy: uuid("created_by"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_marketing_campaigns_scheduled").using("btree", table.scheduledAt.asc().nullsLast().op("timestamptz_ops")),
	index("idx_marketing_campaigns_status").using("btree", table.status.asc().nullsLast().op("enum_ops")),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [adminUsers.id],
			name: "marketing_campaigns_created_by_fkey"
		}),
	foreignKey({
			columns: [table.emailTemplateId],
			foreignColumns: [emailTemplates.id],
			name: "marketing_campaigns_email_template_id_fkey"
		}),
	foreignKey({
			columns: [table.targetSegment],
			foreignColumns: [customerSegments.id],
			name: "marketing_campaigns_target_segment_fkey"
		}),
	pgPolicy("Admin users can manage campaigns", { as: "permissive", for: "all", to: ["public"], using: sql`is_admin_user(auth.uid())` }),
]);

export const websiteMetrics = pgTable("website_metrics", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	date: date().notNull(),
	pagePath: text("page_path").notNull(),
	pageViews: integer("page_views").default(0),
	uniqueVisitors: integer("unique_visitors").default(0),
	bounceRate: numeric("bounce_rate", { precision: 5, scale:  2 }).default('0'),
	avgSessionDuration: integer("avg_session_duration").default(0),
	conversionRate: numeric("conversion_rate", { precision: 5, scale:  2 }).default('0'),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_website_metrics_date").using("btree", table.date.desc().nullsFirst().op("date_ops")),
	unique("website_metrics_date_page_path_key").on(table.date, table.pagePath),
	pgPolicy("Admin users can view metrics", { as: "permissive", for: "select", to: ["public"], using: sql`is_admin_user(auth.uid())` }),
]);

export const adminActivityLogs = pgTable("admin_activity_logs", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	adminUserId: uuid("admin_user_id"),
	performedBy: uuid("performed_by"),
	action: text().notNull(),
	targetType: text("target_type").notNull(),
	targetId: uuid("target_id"),
	details: jsonb().default({}),
	ipAddress: text("ip_address"),
	userAgent: text("user_agent"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("idx_admin_activity_logs_admin_user_id").using("btree", table.adminUserId.asc().nullsLast().op("uuid_ops")),
	index("idx_admin_activity_logs_created_at").using("btree", table.createdAt.asc().nullsLast().op("timestamptz_ops")),
	index("idx_admin_activity_logs_performed_by").using("btree", table.performedBy.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.adminUserId],
			foreignColumns: [adminUsers.id],
			name: "admin_activity_logs_admin_user_id_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.performedBy],
			foreignColumns: [users.id],
			name: "admin_activity_logs_performed_by_fkey"
		}).onDelete("set null"),
	pgPolicy("Admin users can create activity logs", { as: "permissive", for: "insert", to: ["public"], withCheck: sql`(EXISTS ( SELECT 1
   FROM admin_users
  WHERE ((admin_users.id = auth.uid()) AND (admin_users.is_active = true))))`  }),
	pgPolicy("Admin users can view activity logs", { as: "permissive", for: "select", to: ["public"] }),
]);

export const appointmentTypes = pgTable("appointment_types", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	name: text().notNull(),
	description: text(),
	durationMinutes: integer("duration_minutes").default(60).notNull(),
	price: numeric({ precision: 10, scale:  2 }),
	color: text().default('#3B82F6'),
	isActive: boolean("is_active").default(true),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	pgPolicy("Admin can manage appointment types", { as: "permissive", for: "all", to: ["public"], using: sql`(EXISTS ( SELECT 1
   FROM admin_users
  WHERE ((admin_users.id = auth.uid()) AND (admin_users.is_active = true))))` }),
	pgPolicy("Anyone can view appointment types", { as: "permissive", for: "select", to: ["public"] }),
]);

export const timeSlots = pgTable("time_slots", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	doctorId: uuid("doctor_id"),
	date: date().notNull(),
	startTime: time("start_time").notNull(),
	endTime: time("end_time").notNull(),
	isAvailable: boolean("is_available").default(true),
	isRecurring: boolean("is_recurring").default(false),
	recurringPattern: jsonb("recurring_pattern"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.doctorId],
			foreignColumns: [userProfiles.id],
			name: "time_slots_doctor_id_fkey"
		}),
	pgPolicy("Admin can manage time slots", { as: "permissive", for: "all", to: ["public"], using: sql`(EXISTS ( SELECT 1
   FROM admin_users
  WHERE ((admin_users.id = auth.uid()) AND (admin_users.is_active = true))))` }),
	pgPolicy("Anyone can view available time slots", { as: "permissive", for: "select", to: ["public"] }),
]);

export const payloadMigrations = pgTable("payload_migrations", {
	id: serial().primaryKey().notNull(),
	name: varchar(),
	batch: numeric(),
	updatedAt: timestamp("updated_at", { precision: 3, withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	createdAt: timestamp("created_at", { precision: 3, withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("payload_migrations_created_at_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamptz_ops")),
	index("payload_migrations_updated_at_idx").using("btree", table.updatedAt.asc().nullsLast().op("timestamptz_ops")),
]);

export const payloadLockedDocuments = pgTable("payload_locked_documents", {
	id: serial().primaryKey().notNull(),
	globalSlug: varchar("global_slug"),
	updatedAt: timestamp("updated_at", { precision: 3, withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	createdAt: timestamp("created_at", { precision: 3, withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("payload_locked_documents_created_at_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamptz_ops")),
	index("payload_locked_documents_global_slug_idx").using("btree", table.globalSlug.asc().nullsLast().op("text_ops")),
	index("payload_locked_documents_updated_at_idx").using("btree", table.updatedAt.asc().nullsLast().op("timestamptz_ops")),
]);

export const payloadLockedDocumentsRels = pgTable("payload_locked_documents_rels", {
	id: serial().primaryKey().notNull(),
	order: integer(),
	parentId: integer("parent_id").notNull(),
	path: varchar().notNull(),
	usersId: integer("users_id"),
	mediaId: integer("media_id"),
}, (table) => [
	index("payload_locked_documents_rels_media_id_idx").using("btree", table.mediaId.asc().nullsLast().op("int4_ops")),
	index("payload_locked_documents_rels_order_idx").using("btree", table.order.asc().nullsLast().op("int4_ops")),
	index("payload_locked_documents_rels_parent_idx").using("btree", table.parentId.asc().nullsLast().op("int4_ops")),
	index("payload_locked_documents_rels_path_idx").using("btree", table.path.asc().nullsLast().op("text_ops")),
	index("payload_locked_documents_rels_users_id_idx").using("btree", table.usersId.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.parentId],
			foreignColumns: [payloadLockedDocuments.id],
			name: "payload_locked_documents_rels_parent_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.usersId],
			foreignColumns: [users.id],
			name: "payload_locked_documents_rels_users_fk"
		}).onDelete("cascade"),
]);

export const users = pgTable("users", {
	id: serial().primaryKey().notNull(),
	updatedAt: timestamp("updated_at", { precision: 3, withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	createdAt: timestamp("created_at", { precision: 3, withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	email: varchar().notNull(),
	resetPasswordToken: varchar("reset_password_token"),
	resetPasswordExpiration: timestamp("reset_password_expiration", { precision: 3, withTimezone: true, mode: 'string' }),
	salt: varchar(),
	hash: varchar(),
	loginAttempts: numeric("login_attempts").default('0'),
	lockUntil: timestamp("lock_until", { precision: 3, withTimezone: true, mode: 'string' }),
}, (table) => [
	index("users_created_at_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamptz_ops")),
	uniqueIndex("users_email_idx").using("btree", table.email.asc().nullsLast().op("text_ops")),
	index("users_updated_at_idx").using("btree", table.updatedAt.asc().nullsLast().op("timestamptz_ops")),
]);

export const payloadPreferences = pgTable("payload_preferences", {
	id: serial().primaryKey().notNull(),
	key: varchar(),
	value: jsonb(),
	updatedAt: timestamp("updated_at", { precision: 3, withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	createdAt: timestamp("created_at", { precision: 3, withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("payload_preferences_created_at_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamptz_ops")),
	index("payload_preferences_key_idx").using("btree", table.key.asc().nullsLast().op("text_ops")),
	index("payload_preferences_updated_at_idx").using("btree", table.updatedAt.asc().nullsLast().op("timestamptz_ops")),
]);

export const payloadPreferencesRels = pgTable("payload_preferences_rels", {
	id: serial().primaryKey().notNull(),
	order: integer(),
	parentId: integer("parent_id").notNull(),
	path: varchar().notNull(),
	usersId: integer("users_id"),
}, (table) => [
	index("payload_preferences_rels_order_idx").using("btree", table.order.asc().nullsLast().op("int4_ops")),
	index("payload_preferences_rels_parent_idx").using("btree", table.parentId.asc().nullsLast().op("int4_ops")),
	index("payload_preferences_rels_path_idx").using("btree", table.path.asc().nullsLast().op("text_ops")),
	index("payload_preferences_rels_users_id_idx").using("btree", table.usersId.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.parentId],
			foreignColumns: [payloadPreferences.id],
			name: "payload_preferences_rels_parent_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.usersId],
			foreignColumns: [users.id],
			name: "payload_preferences_rels_users_fk"
		}).onDelete("cascade"),
]);

export const media = pgTable("media", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	alt: text(),
	caption: text(),
	category: text(),
	prefix: text(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	url: text(),
	thumbnailURL: text("thumbnail_u_r_l"),
	filename: text(),
	mimeType: text("mime_type"),
	filesize: integer(),
	width: integer(),
	height: integer(),
	focalX: integer("focal_x"),
	focalY: integer("focal_y"),
	sizesThumbnailUrl: text("sizes_thumbnail_url"),
	sizesThumbnailWidth: integer("sizes_thumbnail_width"),
	sizesThumbnailHeight: integer("sizes_thumbnail_height"),
	sizesThumbnailMimeType: text("sizes_thumbnail_mime_type"),
	sizesThumbnailFilesize: integer("sizes_thumbnail_filesize"),
	sizesThumbnailFilename: text("sizes_thumbnail_filename"),
	sizesCardUrl: text("sizes_card_url"),
	sizesCardWidth: integer("sizes_card_width"),
	sizesCardHeight: integer("sizes_card_height"),
	sizesCardMimeType: text("sizes_card_mime_type"),
	sizesCardFilesize: integer("sizes_card_filesize"),
	sizesCardFilename: text("sizes_card_filename"),
	sizesTabletUrl: text("sizes_tablet_url"),
	sizesTabletWidth: integer("sizes_tablet_width"),
	sizesTabletHeight: integer("sizes_tablet_height"),
	sizesTabletMimeType: text("sizes_tablet_mime_type"),
	sizesTabletFilesize: integer("sizes_tablet_filesize"),
	sizesTabletFilename: text("sizes_tablet_filename"),
	sizesDesktopUrl: text("sizes_desktop_url"),
	sizesDesktopWidth: integer("sizes_desktop_width"),
	sizesDesktopHeight: integer("sizes_desktop_height"),
	sizesDesktopMimeType: text("sizes_desktop_mime_type"),
	sizesDesktopFilesize: integer("sizes_desktop_filesize"),
	sizesDesktopFilename: text("sizes_desktop_filename"),
});

export const cmsPageCategories = pgTable("cms_page_categories", {
	pageId: uuid("page_id").notNull(),
	categoryId: uuid("category_id").notNull(),
}, (table) => [
	foreignKey({
			columns: [table.categoryId],
			foreignColumns: [blogCategories.id],
			name: "cms_page_categories_category_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.pageId],
			foreignColumns: [cmsPages.id],
			name: "cms_page_categories_page_id_fkey"
		}).onDelete("cascade"),
	primaryKey({ columns: [table.pageId, table.categoryId], name: "cms_page_categories_pkey"}),
]);

export const cmsPageTags = pgTable("cms_page_tags", {
	pageId: uuid("page_id").notNull(),
	tagId: uuid("tag_id").notNull(),
}, (table) => [
	foreignKey({
			columns: [table.pageId],
			foreignColumns: [cmsPages.id],
			name: "cms_page_tags_page_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.tagId],
			foreignColumns: [blogTags.id],
			name: "cms_page_tags_tag_id_fkey"
		}).onDelete("cascade"),
	primaryKey({ columns: [table.pageId, table.tagId], name: "cms_page_tags_pkey"}),
]);

export const segmentMemberships = pgTable("segment_memberships", {
	segmentId: uuid("segment_id").notNull(),
	patientId: uuid("patient_id").notNull(),
	addedBy: uuid("added_by"),
	addedAt: timestamp("added_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.addedBy],
			foreignColumns: [adminUsers.id],
			name: "segment_memberships_added_by_fkey"
		}),
	foreignKey({
			columns: [table.patientId],
			foreignColumns: [userProfiles.id],
			name: "segment_memberships_patient_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.segmentId],
			foreignColumns: [customerSegments.id],
			name: "segment_memberships_segment_id_fkey"
		}).onDelete("cascade"),
	primaryKey({ columns: [table.segmentId, table.patientId], name: "segment_memberships_pkey"}),
]);
