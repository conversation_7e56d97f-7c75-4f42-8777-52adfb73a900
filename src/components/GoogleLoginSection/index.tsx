'use client'

import React from 'react'
import GoogleLoginButton from '@/components/GoogleLoginButton'

const GoogleLoginSection: React.FC = () => {
  const handleGoogleSuccess = (result: any) => {
    console.log('Google login initiated:', result)
  }

  const handleGoogleError = (error: any) => {
    console.error('Google login error:', error)
    alert('Google login failed. Please try again or use email/password login.')
  }

  return (
    <GoogleLoginButton
      onSuccess={handleGoogleSuccess}
      onError={handleGoogleError}
      className="max-w-sm"
    />
  )
}

export default GoogleLoginSection
