import React from 'react'

const BeforeLogin: React.FC = () => {
  return (
    <div
      style={{
        padding: '20px',
        backgroundColor: '#f8f9fa',
        borderRadius: '8px',
        marginBottom: '20px',
      }}
    >
      <div style={{ marginBottom: '16px' }}>
        <h3 style={{ color: '#2c3e50', marginBottom: '12px' }}>🔐 Lullaby Clinic Admin Access</h3>
        <p style={{ color: '#34495e', marginBottom: '12px' }}>
          <strong>Authorized Personnel Only</strong>
        </p>
        <p style={{ color: '#7f8c8d', fontSize: '14px', lineHeight: '1.5' }}>
          Access to this admin panel is restricted to users who have been pre-approved and verified
          in our authorization system. Only registered and verified users can log in.
        </p>
      </div>

      <div
        style={{
          padding: '12px',
          backgroundColor: '#e8f4f8',
          borderLeft: '4px solid #3498db',
          borderRadius: '4px',
          marginBottom: '16px',
        }}
      >
        <p style={{ color: '#2980b9', fontSize: '13px', margin: '0' }}>
          <strong>Security Notice:</strong> All login attempts are verified against our Supabase
          authentication database. Unauthorized access attempts will be blocked.
        </p>
      </div>

      <div style={{ fontSize: '13px', color: '#95a5a6' }}>
        <p style={{ margin: '0 0 8px 0' }}>
          <strong>Need Access?</strong> Contact your system administrator to be added to the
          authorized users list.
        </p>
        <p style={{ margin: '0' }}>
          <strong>Having Issues?</strong> Ensure your email is verified in the authorization system.
        </p>
      </div>
    </div>
  )
}

export default BeforeLogin
