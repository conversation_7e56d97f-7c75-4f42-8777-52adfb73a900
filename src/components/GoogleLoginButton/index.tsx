'use client'

import React, { useState } from 'react'
import { signInWithGoogle } from '@/lib/supabaseClient'

interface GoogleLoginButtonProps {
  onSuccess?: (user: any) => void
  onError?: (error: any) => void
  disabled?: boolean
  className?: string
  mode?: 'signin' | 'signup'
}

const GoogleLoginButton: React.FC<GoogleLoginButtonProps> = ({
  onSuccess,
  onError,
  disabled = false,
  className = '',
  mode = 'signin',
}) => {
  const [isLoading, setIsLoading] = useState(false)

  const handleSocialAuth = async (provider: 'google') => {
    if (disabled || isLoading) return

    setIsLoading(true)

    try {
      console.log('🔐 Initiating Google OAuth login...')

      const result = await signInWithGoogle()

      if (result) {
        console.log('✅ Google OAuth initiated successfully')
        onSuccess?.(result)
      }
    } catch (error: any) {
      console.error('❌ Google login error:', error)
      onError?.(error)
    } finally {
      setIsLoading(false)
    }
  }

  const buttonText = mode === 'signin' ? 'Sign in' : 'Sign up'

  return (
    <div className="space-y-3">
      <button
        type="button"
        className={`
          w-full flex items-center justify-center gap-3 px-4 py-3
          border border-gray-300 rounded-lg shadow-sm bg-white
          hover:bg-gray-50 focus:outline-none focus:ring-2
          focus:ring-offset-2 focus:ring-blue-500
          disabled:opacity-50 disabled:cursor-not-allowed
          transition-colors duration-200 font-medium
          ${className}
        `}
        onClick={() => handleSocialAuth('google')}
        disabled={disabled || isLoading}
      >
        {isLoading ? (
          <>
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-600"></div>
            <span className="text-gray-600 font-medium">Signing in...</span>
          </>
        ) : (
          <>
            <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="currentColor"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="currentColor"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="currentColor"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
            </svg>
            {buttonText} with Google
          </>
        )}
      </button>
    </div>
  )
}

export default GoogleLoginButton
