import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
    -- Add Supabase Auth fields to users table
    ALTER TABLE "users" 
    ADD COLUMN IF NOT EXISTS "supabase_auth_id" varchar,
    ADD COLUMN IF NOT EXISTS "is_supabase_verified" boolean DEFAULT false;
    
    -- Create index on supabase_auth_id for faster lookups
    CREATE INDEX IF NOT EXISTS "idx_users_supabase_auth_id" ON "users" ("supabase_auth_id");
    
    -- Create index on is_supabase_verified for filtering
    CREATE INDEX IF NOT EXISTS "idx_users_is_supabase_verified" ON "users" ("is_supabase_verified");
  `)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
    -- Remove indexes
    DROP INDEX IF EXISTS "idx_users_supabase_auth_id";
    DROP INDEX IF EXISTS "idx_users_is_supabase_verified";
    
    -- Remove Supabase Auth fields from users table
    ALTER TABLE "users" 
    DROP COLUMN IF EXISTS "supabase_auth_id",
    DROP COLUMN IF EXISTS "is_supabase_verified";
  `)
}
