// storage-adapter-import-placeholder
import { postgresAdapter } from '@payloadcms/db-postgres'
import { payloadCloudPlugin } from '@payloadcms/payload-cloud'
import { s3Storage } from '@payloadcms/storage-s3'
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import path from 'path'
import { buildConfig } from 'payload'
import { fileURLToPath } from 'url'
import sharp from 'sharp'

import { Users } from './collections/Users'
import { Media } from './collections/Media'
import { Appointments } from './collections/Appointments'
import { Doctors } from './collections/Doctors'
import { Services } from './collections/Services'
import { TimeSlots } from './collections/TimeSlots'
import { ClinicSettings } from './collections/ClinicSettings'
import { UserProfiles } from './collections/UserProfiles'
import { AdminProfiles } from './collections/AdminProfiles'
import { Invoices } from './collections/Invoices'
import { Payments } from './collections/Payments'
import { Promotions } from './collections/Promotions'
import { InventoryItems } from './collections/InventoryItems'
import { BlogPosts } from './collections/BlogPosts'
import { CmsPages } from './collections/CmsPages'
import { MarketingCampaigns } from './collections/MarketingCampaigns'
import { Reviews } from './collections/Reviews'
import { AnalyticsEvents } from './collections/AnalyticsEvents'
import { KpiMetrics } from './collections/KpiMetrics'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

const prefix = process.env.S3_PREFIX || 'uploads'

export default buildConfig({
  admin: {
    user: Users.slug,
    importMap: {
      baseDir: path.resolve(dirname),
    },
  },
  collections: [
    Users,
    Media,
    Appointments,
    Doctors,
    Services,
    TimeSlots,
    ClinicSettings,
    UserProfiles,
    AdminProfiles,
    Invoices,
    Payments,
    Promotions,
    InventoryItems,
    BlogPosts,
    CmsPages,
    MarketingCampaigns,
    Reviews,
    AnalyticsEvents,
    KpiMetrics,
  ],
  editor: lexicalEditor(),
  secret: process.env.PAYLOAD_SECRET || '',
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  db: postgresAdapter({
    pool: {
      connectionString: process.env.DATABASE_URI || '',
    },
    push: false,
    migrationDir: './drizzle',
  }),
  sharp,
  plugins: [
    payloadCloudPlugin(),
    s3Storage({
      collections: {
        media: {
          prefix,
          signedDownloads: {
            shouldUseSignedURL: ({ collection, filename, req }) => {
              return filename.endsWith('.mp4')
            },
          },
        },
      },
      bucket: process.env.S3_BUCKET || '',
      config: {
        credentials: {
          accessKeyId: process.env.S3_ACCESS_KEY_ID || '',
          secretAccessKey: process.env.S3_SECRET_ACCESS_KEY || '',
        },
        region: process.env.S3_REGION || '',
        endpoint: process.env.S3_ENDPOINT || '',
        forcePathStyle: true,
        s3ForcePathStyle: true,
        signatureVersion: 'v4',
      },
    }),
    // storage-adapter-placeholder
  ],
})
