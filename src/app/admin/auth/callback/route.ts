import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabaseClient'
import { checkLoginPermission, getSupabaseAuthUser } from '@/lib/supabaseAuth'

export async function GET(request: NextRequest) {
  const { searchParams, origin } = new URL(request.url)
  const code = searchParams.get('code')
  const next = searchParams.get('next') ?? '/admin'

  if (code) {
    try {
      console.log('🔐 Processing Google OAuth callback...')

      // Exchange the code for a session
      const {
        data: { session },
        error,
      } = await supabase.auth.exchangeCodeForSession(code)

      if (error) {
        console.error('❌ Error exchanging code for session:', error)
        return NextResponse.redirect(`${origin}/admin/login?error=oauth_error`)
      }

      if (!session?.user?.email) {
        console.error('❌ No user email in session')
        return NextResponse.redirect(`${origin}/admin/login?error=no_email`)
      }

      const userEmail = session.user.email
      console.log(`🔍 Verifying Google OAuth user: ${userEmail}`)

      // Verify the user is authorized in our system
      const { allowed, reason } = await checkLoginPermission(userEmail)

      if (!allowed) {
        console.log(`🚫 Google OAuth login blocked for ${userEmail}: ${reason}`)

        // Sign out the user from Supabase since they're not authorized
        await supabase.auth.signOut()

        return NextResponse.redirect(
          `${origin}/admin/login?error=unauthorized&message=${encodeURIComponent(reason || 'Access denied')}`,
        )
      }

      console.log(`✅ Google OAuth login authorized for: ${userEmail}`)

      // Get additional user details from Supabase Auth
      const supabaseUser = await getSupabaseAuthUser(userEmail)

      if (supabaseUser) {
        console.log(`✅ User verified in Supabase Auth: ${userEmail}`)

        // Sync user with Payload CMS
        try {
          const syncResponse = await fetch(`${origin}/api/google-oauth-sync`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: userEmail,
              supabaseUserId: session.user.id,
              userData: {
                name: session.user.user_metadata?.full_name || session.user.user_metadata?.name,
                avatar: session.user.user_metadata?.avatar_url,
                provider: 'google',
              },
            }),
          })

          const syncResult = await syncResponse.json()

          if (!syncResponse.ok || !syncResult.success) {
            console.error('❌ Failed to sync user with Payload CMS:', syncResult.error)
            await supabase.auth.signOut()
            return NextResponse.redirect(
              `${origin}/admin/login?error=sync_failed&message=${encodeURIComponent(syncResult.error || 'Failed to sync user')}`,
            )
          }

          console.log(`✅ User synced with Payload CMS: ${userEmail}`)

          // Redirect to admin dashboard - the user is already authenticated via Supabase
          return NextResponse.redirect(`${origin}${next}`)
        } catch (syncError) {
          console.error('❌ Error syncing user with Payload CMS:', syncError)
          await supabase.auth.signOut()
          return NextResponse.redirect(`${origin}/admin/login?error=sync_error`)
        }
      } else {
        console.error(`❌ User not found in Supabase Auth: ${userEmail}`)
        await supabase.auth.signOut()
        return NextResponse.redirect(`${origin}/admin/login?error=user_not_found`)
      }
    } catch (error) {
      console.error('❌ Error in OAuth callback:', error)
      return NextResponse.redirect(`${origin}/admin/login?error=callback_error`)
    }
  }

  // If no code, redirect to login with error
  console.error('❌ No authorization code in OAuth callback')
  return NextResponse.redirect(`${origin}/admin/login?error=no_code`)
}
