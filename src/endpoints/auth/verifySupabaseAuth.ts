import type { Payload<PERSON>and<PERSON> } from 'payload'
import { checkRegistrationPermission, checkLoginPermission, getSupabaseAuthUser } from '@/lib/supabaseAuth'

/**
 * Endpoint to verify if a user is authorized in Supabase Auth
 * This can be called before registration or login attempts
 */
export const verifySupabaseAuthHandler: PayloadHandler = async (req): Promise<Response> => {
  try {
    const { email, action } = await req.json()

    if (!email) {
      return Response.json(
        { 
          error: 'Email is required',
          allowed: false 
        }, 
        { status: 400 }
      )
    }

    if (!action || !['register', 'login'].includes(action)) {
      return Response.json(
        { 
          error: 'Action must be either "register" or "login"',
          allowed: false 
        }, 
        { status: 400 }
      )
    }

    let result: { allowed: boolean, reason?: string }

    if (action === 'register') {
      result = await checkRegistrationPermission(email)
    } else {
      result = await checkLoginPermission(email)
    }

    if (!result.allowed) {
      return Response.json(
        {
          allowed: false,
          error: result.reason || 'Access denied',
          action
        },
        { status: 403 }
      )
    }

    // Get user details from Supabase Auth for additional info
    const supabaseUser = await getSupabaseAuthUser(email)

    return Response.json({
      allowed: true,
      action,
      supabaseUser: supabaseUser ? {
        id: supabaseUser.id,
        email: supabaseUser.email,
        email_confirmed_at: supabaseUser.email_confirmed_at,
        created_at: supabaseUser.created_at,
        last_sign_in_at: supabaseUser.last_sign_in_at
      } : null
    })

  } catch (error) {
    console.error('❌ Error in verifySupabaseAuth endpoint:', error)
    return Response.json(
      { 
        error: 'Internal server error',
        allowed: false 
      }, 
      { status: 500 }
    )
  }
}
