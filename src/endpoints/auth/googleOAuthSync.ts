import type { PayloadHand<PERSON> } from 'payload'
import { checkLoginPermission, getSupabaseAuthUser } from '@/lib/supabaseAuth'

/**
 * Endpoint to sync Google OAuth users with Payload CMS
 * This creates or updates users in Payload after successful Google OAuth
 */
export const googleOAuthSyncHandler: PayloadHandler = async (req): Promise<Response> => {
  try {
    const { email, supabaseUserId, userData } = await req.json()

    if (!email || !supabaseUserId) {
      return Response.json(
        {
          error: 'Email and Supabase user ID are required',
          success: false,
        },
        { status: 400 },
      )
    }

    console.log(`🔐 Syncing Google OAuth user with Payload CMS: ${email}`)

    // Verify the user is authorized in Supabase Auth
    const { allowed, reason } = await checkLoginPermission(email)

    if (!allowed) {
      console.log(`🚫 Google OAuth sync blocked for ${email}: ${reason}`)
      return Response.json(
        {
          error: reason || 'User not authorized',
          success: false,
          code: 'UNAUTHORIZED_USER',
        },
        { status: 403 },
      )
    }

    // Get detailed user info from Supabase Auth
    const supabaseUser = await getSupabaseAuthUser(email)

    if (!supabaseUser) {
      console.log(`🚫 Google OAuth sync blocked for ${email}: User not found in Supabase Auth`)
      return Response.json(
        {
          error: 'User not found in authorization system',
          success: false,
          code: 'USER_NOT_FOUND',
        },
        { status: 403 },
      )
    }

    console.log(`✅ Google OAuth user authorized: ${email}`)

    try {
      // For Google OAuth users, we'll create a simplified approach
      // First try to create the user, if it exists we'll get an error and then update
      let payloadUser
      let isNewUser = false

      try {
        // Try to create new user first
        payloadUser = await req.payload.create({
          collection: 'users',
          data: {
            email,
            // Generate a random password since Google OAuth doesn't use passwords
            password:
              Math.random().toString(36).substring(2, 15) +
              Math.random().toString(36).substring(2, 15),
            supabaseAuthId: supabaseUser.id,
            isSupabaseVerified: true,
          },
        })

        isNewUser = true
        console.log(`✅ Created new Payload user: ${email}`)
      } catch (createError: any) {
        // If user already exists, find and update them
        if (createError.message?.includes('duplicate') || createError.message?.includes('unique')) {
          console.log(`ℹ️  User already exists, updating: ${email}`)

          // Find the existing user by email using a simpler query
          const existingUsers = await req.payload.find({
            collection: 'users',
            where: {
              email: {
                equals: email,
              },
            },
            limit: 1,
            depth: 0, // Avoid deep relationships that might cause issues
          })

          if (existingUsers.docs.length > 0) {
            // Update the existing user with minimal data to avoid relationship issues
            payloadUser = existingUsers.docs[0]

            // Update Supabase verification fields directly in the database
            await req.payload.db.drizzle
              .update(req.payload.db.tables.users)
              .set({
                supabaseAuthId: supabaseUser.id,
                isSupabaseVerified: true,
                updatedAt: new Date(),
              })
              .where(req.payload.db.drizzle.eq(req.payload.db.tables.users.id, payloadUser.id))

            console.log(`✅ Updated existing Payload user: ${email}`)
          } else {
            throw new Error('User exists but could not be found for update')
          }
        } else {
          throw createError
        }
      }

      // For OAuth users, we'll generate a simple success response
      // The actual authentication will be handled by the OAuth callback
      console.log(`✅ Google OAuth sync completed for: ${email}`)

      return Response.json({
        success: true,
        message: 'User synced successfully',
        user: {
          id: payloadUser.id,
          email: payloadUser.email,
          supabaseAuthId: supabaseUser.id,
          isSupabaseVerified: true,
          createdAt: payloadUser.createdAt,
          updatedAt: payloadUser.updatedAt || new Date().toISOString(),
        },
        isNewUser,
      })
    } catch (payloadError: any) {
      console.error('❌ Error syncing user with Payload CMS:', payloadError)

      return Response.json(
        {
          error: 'Failed to sync user with CMS',
          success: false,
          code: 'SYNC_FAILED',
          details: payloadError.message,
        },
        { status: 500 },
      )
    }
  } catch (error) {
    console.error('❌ Error in Google OAuth sync endpoint:', error)
    return Response.json(
      {
        error: 'Internal server error',
        success: false,
        code: 'INTERNAL_ERROR',
      },
      { status: 500 },
    )
  }
}
