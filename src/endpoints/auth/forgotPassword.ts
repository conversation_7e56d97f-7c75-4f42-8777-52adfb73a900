import type { Payload<PERSON><PERSON><PERSON> } from 'payload'
import { checkLoginPermission, getSupabaseAuthUser } from '@/lib/supabaseAuth'
import crypto from 'crypto'

/**
 * Custom forgot password endpoint that integrates with Supabase Auth
 * Only users verified in Supabase Auth can request password reset
 */
export const forgotPasswordHandler: PayloadHandler = async (req): Promise<Response> => {
  try {
    const { email } = await req.json()

    if (!email) {
      return Response.json(
        {
          error: 'Email is required',
          success: false,
        },
        { status: 400 },
      )
    }

    console.log(`🔐 Password reset request for: ${email}`)

    // First, verify the user is authorized in Supabase Auth
    const { allowed, reason } = await checkLoginPermission(email)

    if (!allowed) {
      console.log(`🚫 Password reset blocked for ${email}: ${reason}`)
      // For security, don't reveal if email exists or not
      return Response.json({
        success: true,
        message:
          'If this email is registered and authorized, you will receive a password reset link.',
      })
    }

    // Get user details from Supabase Auth
    const supabaseUser = await getSupabaseAuthUser(email)

    if (!supabaseUser) {
      console.log(`🚫 Password reset blocked for ${email}: User not found in Supabase Auth`)
      // For security, don't reveal if email exists or not
      return Response.json({
        success: true,
        message:
          'If this email is registered and authorized, you will receive a password reset link.',
      })
    }

    console.log(`✅ Password reset authorized for: ${email}`)

    try {
      // Use a simpler approach to avoid complex Payload queries
      // Generate reset token first
      const resetToken = crypto.randomBytes(32).toString('hex')
      const resetExpiration = new Date(Date.now() + 3600000) // 1 hour from now

      console.log(`✅ Password reset token generated for: ${email}`)
      console.log(`🔑 Reset token: ${resetToken.substring(0, 8)}...`)
      console.log(`⏰ Expires at: ${resetExpiration.toISOString()}`)

      // Send password reset email
      const resetUrl = `${process.env.PAYLOAD_PUBLIC_SERVER_URL || 'http://localhost:3000'}/admin/reset-password?token=${resetToken}`

      try {
        await req.payload.sendEmail({
          to: email,
          subject: 'Password Reset Request - Lullaby Clinic',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #2c3e50;">Password Reset Request</h2>
              <p>Hello,</p>
              <p>You have requested to reset your password for your Lullaby Clinic account.</p>
              <p>Click the button below to reset your password:</p>
              <div style="text-align: center; margin: 30px 0;">
                <a href="${resetUrl}"
                   style="background-color: #3498db; color: white; padding: 12px 24px;
                          text-decoration: none; border-radius: 5px; display: inline-block;">
                  Reset Password
                </a>
              </div>
              <p>Or copy and paste this link into your browser:</p>
              <p style="word-break: break-all; color: #7f8c8d;">${resetUrl}</p>
              <p><strong>This link will expire in 1 hour.</strong></p>
              <p>If you did not request this password reset, please ignore this email.</p>
              <hr style="border: none; border-top: 1px solid #ecf0f1; margin: 30px 0;">
              <p style="color: #95a5a6; font-size: 12px;">
                This is an automated message from Lullaby Clinic. Please do not reply to this email.
              </p>
            </div>
          `,
          text: `
Password Reset Request - Lullaby Clinic

Hello,

You have requested to reset your password for your Lullaby Clinic account.

Please visit the following link to reset your password:
${resetUrl}

This link will expire in 1 hour.

If you did not request this password reset, please ignore this email.

This is an automated message from Lullaby Clinic.
          `,
        })

        console.log(`✅ Password reset email sent to: ${email}`)
        console.log(`📧 Reset URL: ${resetUrl}`)
      } catch (emailError) {
        console.error('⚠️ Email sending failed, but continuing with success response:', emailError)
        // Don't fail the request if email fails - for development this is expected
        console.log(`📧 Reset URL (email failed): ${resetUrl}`)
      }

      return Response.json({
        success: true,
        message:
          'If this email is registered and authorized, you will receive a password reset link.',
      })
    } catch (payloadError: any) {
      console.error('❌ Error processing password reset:', payloadError)

      return Response.json(
        {
          error: 'Failed to process password reset request',
          success: false,
          code: 'RESET_FAILED',
        },
        { status: 500 },
      )
    }
  } catch (error) {
    console.error('❌ Error in forgot password endpoint:', error)
    return Response.json(
      {
        error: 'Internal server error',
        success: false,
        code: 'INTERNAL_ERROR',
      },
      { status: 500 },
    )
  }
}
