import type { Pay<PERSON><PERSON><PERSON><PERSON> } from 'payload'
import { checkLoginPermission } from '@/lib/supabaseAuth'

/**
 * Password reset endpoint that verifies token and updates password
 * Maintains integration with Supabase Auth security
 */
export const resetPasswordHandler: PayloadHandler = async (req): Promise<Response> => {
  try {
    const { token, password } = await req.json()

    if (!token || !password) {
      return Response.json(
        { 
          error: 'Reset token and new password are required',
          success: false 
        }, 
        { status: 400 }
      )
    }

    if (password.length < 8) {
      return Response.json(
        { 
          error: 'Password must be at least 8 characters long',
          success: false 
        }, 
        { status: 400 }
      )
    }

    console.log(`🔐 Password reset attempt with token: ${token.substring(0, 8)}...`)

    try {
      // Find user by reset token
      const users = await req.payload.find({
        collection: 'users',
        where: {
          resetPasswordToken: {
            equals: token
          }
        },
        limit: 1,
        depth: 0
      })

      if (users.docs.length === 0) {
        console.log(`❌ Invalid reset token: ${token.substring(0, 8)}...`)
        return Response.json(
          {
            error: 'Invalid or expired reset token',
            success: false,
            code: 'INVALID_TOKEN'
          },
          { status: 400 }
        )
      }

      const user = users.docs[0]

      // Check if token has expired
      if (!user.resetPasswordExpiration || new Date() > new Date(user.resetPasswordExpiration)) {
        console.log(`❌ Expired reset token for user: ${user.email}`)
        
        // Clear expired token
        await req.payload.update({
          collection: 'users',
          id: user.id,
          data: {
            resetPasswordToken: null,
            resetPasswordExpiration: null
          }
        })

        return Response.json(
          {
            error: 'Reset token has expired. Please request a new password reset.',
            success: false,
            code: 'TOKEN_EXPIRED'
          },
          { status: 400 }
        )
      }

      console.log(`✅ Valid reset token for user: ${user.email}`)

      // Verify the user is still authorized in Supabase Auth
      const { allowed, reason } = await checkLoginPermission(user.email)

      if (!allowed) {
        console.log(`🚫 Password reset blocked for ${user.email}: ${reason}`)
        return Response.json(
          {
            error: 'Account access has been revoked. Contact administrator.',
            success: false,
            code: 'ACCESS_REVOKED'
          },
          { status: 403 }
        )
      }

      // Update password and clear reset token
      await req.payload.update({
        collection: 'users',
        id: user.id,
        data: {
          password: password,
          resetPasswordToken: null,
          resetPasswordExpiration: null,
          // Clear any login attempts/locks
          loginAttempts: 0,
          lockUntil: null
        }
      })

      console.log(`✅ Password reset successful for: ${user.email}`)

      // Send confirmation email
      try {
        await req.payload.sendEmail({
          to: user.email,
          subject: 'Password Reset Successful - Lullaby Clinic',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #27ae60;">Password Reset Successful</h2>
              <p>Hello,</p>
              <p>Your password has been successfully reset for your Lullaby Clinic account.</p>
              <p>You can now log in with your new password.</p>
              <div style="text-align: center; margin: 30px 0;">
                <a href="${process.env.PAYLOAD_PUBLIC_SERVER_URL || 'http://localhost:3000'}/admin/login" 
                   style="background-color: #27ae60; color: white; padding: 12px 24px; 
                          text-decoration: none; border-radius: 5px; display: inline-block;">
                  Login to Your Account
                </a>
              </div>
              <p>If you did not reset your password, please contact support immediately.</p>
              <hr style="border: none; border-top: 1px solid #ecf0f1; margin: 30px 0;">
              <p style="color: #95a5a6; font-size: 12px;">
                This is an automated message from Lullaby Clinic. Please do not reply to this email.
              </p>
            </div>
          `,
          text: `
Password Reset Successful - Lullaby Clinic

Hello,

Your password has been successfully reset for your Lullaby Clinic account.

You can now log in with your new password at:
${process.env.PAYLOAD_PUBLIC_SERVER_URL || 'http://localhost:3000'}/admin/login

If you did not reset your password, please contact support immediately.

This is an automated message from Lullaby Clinic.
          `
        })

        console.log(`✅ Password reset confirmation email sent to: ${user.email}`)
      } catch (emailError) {
        console.error('⚠️ Failed to send confirmation email:', emailError)
        // Don't fail the password reset if email fails
      }

      return Response.json({
        success: true,
        message: 'Password has been reset successfully. You can now log in with your new password.'
      })

    } catch (payloadError: any) {
      console.error('❌ Error processing password reset:', payloadError)
      
      return Response.json(
        {
          error: 'Failed to reset password',
          success: false,
          code: 'RESET_FAILED'
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('❌ Error in reset password endpoint:', error)
    return Response.json(
      { 
        error: 'Internal server error',
        success: false,
        code: 'INTERNAL_ERROR'
      }, 
      { status: 500 }
    )
  }
}
