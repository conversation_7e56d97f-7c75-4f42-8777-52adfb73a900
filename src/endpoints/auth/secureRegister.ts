import type { Payload<PERSON>andler } from 'payload'
import { checkRegistrationPermission, getSupabaseAuthUser } from '@/lib/supabaseAuth'

/**
 * Secure registration endpoint that enforces Supabase Auth verification
 * Only users pre-approved in Supabase Auth can register
 */
export const secureRegisterHandler: PayloadHandler = async (req): Promise<Response> => {
  try {
    const { email, password } = await req.json()

    if (!email || !password) {
      return Response.json(
        { 
          error: 'Email and password are required',
          success: false 
        }, 
        { status: 400 }
      )
    }

    console.log(`🔐 Secure registration attempt for: ${email}`)

    // First, verify the user is authorized in Supabase Auth
    const { allowed, reason } = await checkRegistrationPermission(email)

    if (!allowed) {
      console.log(`🚫 Registration blocked for ${email}: ${reason}`)
      return Response.json(
        {
          error: reason || 'Registration not authorized',
          success: false,
          code: 'UNAUTHORIZED_REGISTRATION'
        },
        { status: 403 }
      )
    }

    // Get Supabase user details for additional verification
    const supabaseUser = await getSupabaseAuthUser(email)
    
    if (!supabaseUser) {
      console.log(`🚫 Registration blocked for ${email}: User not found in Supabase Auth`)
      return Response.json(
        {
          error: 'User not found in authorization system',
          success: false,
          code: 'USER_NOT_FOUND'
        },
        { status: 403 }
      )
    }

    if (!supabaseUser.email_confirmed_at) {
      console.log(`🚫 Registration blocked for ${email}: Email not confirmed in Supabase Auth`)
      return Response.json(
        {
          error: 'Email not confirmed in authorization system',
          success: false,
          code: 'EMAIL_NOT_CONFIRMED'
        },
        { status: 403 }
      )
    }

    console.log(`✅ Registration authorized for: ${email}`)

    // If verification passes, create the user in Payload CMS
    try {
      const user = await req.payload.create({
        collection: 'users',
        data: {
          email,
          password,
          supabaseAuthId: supabaseUser.id,
          isSupabaseVerified: true
        }
      })

      console.log(`✅ User created successfully in Payload CMS: ${email}`)

      // Return success response (without sensitive data)
      return Response.json({
        success: true,
        message: 'User registered successfully',
        user: {
          id: user.id,
          email: user.email,
          createdAt: user.createdAt,
          isSupabaseVerified: user.isSupabaseVerified
        }
      })

    } catch (payloadError: any) {
      console.error('❌ Error creating user in Payload CMS:', payloadError)
      
      // Handle duplicate email error
      if (payloadError.message?.includes('duplicate') || payloadError.message?.includes('unique')) {
        return Response.json(
          {
            error: 'User with this email already exists',
            success: false,
            code: 'USER_EXISTS'
          },
          { status: 409 }
        )
      }

      return Response.json(
        {
          error: 'Failed to create user account',
          success: false,
          code: 'CREATION_FAILED'
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('❌ Error in secure registration endpoint:', error)
    return Response.json(
      { 
        error: 'Internal server error',
        success: false,
        code: 'INTERNAL_ERROR'
      }, 
      { status: 500 }
    )
  }
}
