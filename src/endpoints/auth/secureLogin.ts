import type { PayloadHandler } from 'payload'
import { checkLoginPermission, getSupabaseAuthUser } from '@/lib/supabaseAuth'

/**
 * Secure login endpoint that enforces Supabase Auth verification
 * Only users verified in Supabase Auth can login
 */
export const secureLoginHandler: PayloadHandler = async (req): Promise<Response> => {
  try {
    const { email, password } = await req.json()

    if (!email || !password) {
      return Response.json(
        { 
          error: 'Email and password are required',
          success: false 
        }, 
        { status: 400 }
      )
    }

    console.log(`🔐 Secure login attempt for: ${email}`)

    // First, verify the user is authorized in Supabase Auth
    const { allowed, reason } = await checkLoginPermission(email)

    if (!allowed) {
      console.log(`🚫 Login blocked for ${email}: ${reason}`)
      return Response.json(
        {
          error: reason || 'Login not authorized',
          success: false,
          code: 'UNAUTHORIZED_LOGIN'
        },
        { status: 403 }
      )
    }

    // Get Supabase user details for additional verification
    const supabaseUser = await getSupabaseAuthUser(email)
    
    if (!supabaseUser) {
      console.log(`🚫 Login blocked for ${email}: User not found in Supabase Auth`)
      return Response.json(
        {
          error: 'User not found in authorization system',
          success: false,
          code: 'USER_NOT_FOUND'
        },
        { status: 403 }
      )
    }

    if (!supabaseUser.email_confirmed_at) {
      console.log(`🚫 Login blocked for ${email}: Email not confirmed in Supabase Auth`)
      return Response.json(
        {
          error: 'Email not confirmed in authorization system',
          success: false,
          code: 'EMAIL_NOT_CONFIRMED'
        },
        { status: 403 }
      )
    }

    console.log(`✅ Login authorized for: ${email}`)

    // If verification passes, attempt login with Payload CMS
    try {
      const result = await req.payload.login({
        collection: 'users',
        data: {
          email,
          password
        }
      })

      if (!result.user) {
        console.log(`🚫 Login failed for ${email}: Invalid credentials`)
        return Response.json(
          {
            error: 'Invalid email or password',
            success: false,
            code: 'INVALID_CREDENTIALS'
          },
          { status: 401 }
        )
      }

      console.log(`✅ Login successful for: ${email}`)

      // Update user's Supabase verification status if needed
      if (!result.user.isSupabaseVerified || result.user.supabaseAuthId !== supabaseUser.id) {
        try {
          await req.payload.update({
            collection: 'users',
            id: result.user.id,
            data: {
              supabaseAuthId: supabaseUser.id,
              isSupabaseVerified: true
            }
          })
          console.log(`✅ Updated Supabase verification status for: ${email}`)
        } catch (updateError) {
          console.error('❌ Error updating user verification status:', updateError)
        }
      }

      // Return success response with token
      return Response.json({
        success: true,
        message: 'Login successful',
        token: result.token,
        user: {
          id: result.user.id,
          email: result.user.email,
          createdAt: result.user.createdAt,
          isSupabaseVerified: true
        }
      })

    } catch (payloadError: any) {
      console.error('❌ Error during Payload login:', payloadError)
      
      // Handle invalid credentials
      if (payloadError.message?.includes('Invalid') || payloadError.message?.includes('credentials')) {
        return Response.json(
          {
            error: 'Invalid email or password',
            success: false,
            code: 'INVALID_CREDENTIALS'
          },
          { status: 401 }
        )
      }

      // Handle account locked
      if (payloadError.message?.includes('locked') || payloadError.message?.includes('attempts')) {
        return Response.json(
          {
            error: 'Account temporarily locked due to too many failed attempts',
            success: false,
            code: 'ACCOUNT_LOCKED'
          },
          { status: 423 }
        )
      }

      return Response.json(
        {
          error: 'Login failed',
          success: false,
          code: 'LOGIN_FAILED'
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('❌ Error in secure login endpoint:', error)
    return Response.json(
      { 
        error: 'Internal server error',
        success: false,
        code: 'INTERNAL_ERROR'
      }, 
      { status: 500 }
    )
  }
}
