import type { CollectionConfig } from 'payload'
import {
  checkRegistrationPermission,
  checkLoginPermission,
  getSupabaseAuthUser,
} from '@/lib/supabaseAuth'

export const Users: CollectionConfig = {
  slug: 'users',
  admin: {
    useAsTitle: 'email',
  },
  auth: {
    forgotPassword: {
      generateEmailHTML: ({ token, user }) => {
        const resetUrl = `${process.env.PAYLOAD_PUBLIC_SERVER_URL || 'http://localhost:3000'}/admin/reset-password?token=${token}`
        return `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #2c3e50;">Password Reset Request</h2>
            <p>Hello,</p>
            <p>You have requested to reset your password for your Lullaby Clinic account.</p>
            <p>Click the button below to reset your password:</p>
            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetUrl}"
                 style="background-color: #3498db; color: white; padding: 12px 24px;
                        text-decoration: none; border-radius: 5px; display: inline-block;">
                Reset Password
              </a>
            </div>
            <p>Or copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #7f8c8d;">${resetUrl}</p>
            <p><strong>This link will expire in 1 hour.</strong></p>
            <p>If you did not request this password reset, please ignore this email.</p>
            <hr style="border: none; border-top: 1px solid #ecf0f1; margin: 30px 0;">
            <p style="color: #95a5a6; font-size: 12px;">
              This is an automated message from Lullaby Clinic. Please do not reply to this email.
            </p>
          </div>
        `
      },
      generateEmailSubject: () => 'Password Reset Request - Lullaby Clinic',
    },
  },
  hooks: {
    beforeOperation: [
      async ({ operation, args }) => {
        // Block registration attempts for unauthorized users
        if (operation === 'create' && args.data?.email) {
          const email = args.data.email

          console.log(`🔐 Checking registration permission for: ${email}`)

          const { allowed, reason } = await checkRegistrationPermission(email)

          if (!allowed) {
            console.log(`🚫 Registration blocked for ${email}: ${reason}`)
            throw new Error(reason || 'Registration not authorized')
          }

          console.log(`✅ Registration allowed for: ${email}`)
        }

        return args
      },
    ],
    beforeLogin: [
      async ({ email }) => {
        // Skip verification for password reset operations
        if (!email) {
          console.log(`🔐 Login attempt without email (possibly password reset)`)
          return true
        }

        console.log(`🔐 Checking login permission for: ${email}`)

        const { allowed, reason } = await checkLoginPermission(email)

        if (!allowed) {
          console.log(`🚫 Login blocked for ${email}: ${reason}`)
          throw new Error(reason || 'Login not authorized')
        }

        console.log(`✅ Login allowed for: ${email}`)
        return true
      },
    ],
    beforeForgotPassword: [
      async ({ args }) => {
        const email = args.data?.email

        if (!email) {
          throw new Error('Email is required for password reset')
        }

        console.log(`🔐 Checking forgot password permission for: ${email}`)

        const { allowed, reason } = await checkLoginPermission(email)

        if (!allowed) {
          console.log(`🚫 Forgot password blocked for ${email}: ${reason}`)
          // For security, don't reveal if email exists or not
          // Just log the block but don't throw error
          return false
        }

        console.log(`✅ Forgot password allowed for: ${email}`)
        return true
      },
    ],
    afterChange: [
      async ({ doc, operation }) => {
        // Sync Supabase Auth data when user is created or updated
        if (operation === 'create' && doc.email) {
          try {
            const supabaseUser = await getSupabaseAuthUser(doc.email)

            if (supabaseUser) {
              // Update the user with Supabase Auth data
              await doc.collection.updateByID(doc.id, {
                supabaseAuthId: supabaseUser.id,
                isSupabaseVerified: !!supabaseUser.email_confirmed_at,
              })

              console.log(`✅ Synced Supabase Auth data for user: ${doc.email}`)
            }
          } catch (error) {
            console.error('❌ Error syncing Supabase Auth data:', error)
          }
        }

        return doc
      },
    ],
  },
  fields: [
    // Email added by default
    // Add more fields as needed
    {
      name: 'supabaseAuthId',
      type: 'text',
      label: 'Supabase Auth ID',
      admin: {
        readOnly: true,
        description: 'ID from Supabase Auth system',
      },
    },
    {
      name: 'isSupabaseVerified',
      type: 'checkbox',
      label: 'Supabase Verified',
      defaultValue: false,
      admin: {
        readOnly: true,
        description: 'Whether user is verified in Supabase Auth system',
      },
    },
  ],
}
