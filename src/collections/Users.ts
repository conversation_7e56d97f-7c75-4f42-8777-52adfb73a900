import type { CollectionConfig } from 'payload'
import {
  checkRegistrationPermission,
  checkLoginPermission,
  getSupabaseAuthUser,
} from '@/lib/supabaseAuth'

export const Users: CollectionConfig = {
  slug: 'users',
  admin: {
    useAsTitle: 'email',
  },
  auth: true,
  hooks: {
    beforeOperation: [
      async ({ operation, args }) => {
        // Block registration attempts for unauthorized users
        if (operation === 'create' && args.data?.email) {
          const email = args.data.email

          console.log(`🔐 Checking registration permission for: ${email}`)

          const { allowed, reason } = await checkRegistrationPermission(email)

          if (!allowed) {
            console.log(`🚫 Registration blocked for ${email}: ${reason}`)
            throw new Error(reason || 'Registration not authorized')
          }

          console.log(`✅ Registration allowed for: ${email}`)
        }

        return args
      },
    ],
    beforeLogin: [
      async ({ email }) => {
        console.log(`🔐 Checking login permission for: ${email}`)

        const { allowed, reason } = await checkLoginPermission(email)

        if (!allowed) {
          console.log(`🚫 Login blocked for ${email}: ${reason}`)
          throw new Error(reason || 'Login not authorized')
        }

        console.log(`✅ Login allowed for: ${email}`)
        return true
      },
    ],
    afterChange: [
      async ({ doc, operation }) => {
        // Sync Supabase Auth data when user is created or updated
        if (operation === 'create' && doc.email) {
          try {
            const supabaseUser = await getSupabaseAuthUser(doc.email)

            if (supabaseUser) {
              // Update the user with Supabase Auth data
              await doc.collection.updateByID(doc.id, {
                supabaseAuthId: supabaseUser.id,
                isSupabaseVerified: !!supabaseUser.email_confirmed_at,
              })

              console.log(`✅ Synced Supabase Auth data for user: ${doc.email}`)
            }
          } catch (error) {
            console.error('❌ Error syncing Supabase Auth data:', error)
          }
        }

        return doc
      },
    ],
  },
  fields: [
    // Email added by default
    // Add more fields as needed
    {
      name: 'supabaseAuthId',
      type: 'text',
      label: 'Supabase Auth ID',
      admin: {
        readOnly: true,
        description: 'ID from Supabase Auth system',
      },
    },
    {
      name: 'isSupabaseVerified',
      type: 'checkbox',
      label: 'Supabase Verified',
      defaultValue: false,
      admin: {
        readOnly: true,
        description: 'Whether user is verified in Supabase Auth system',
      },
    },
  ],
}
