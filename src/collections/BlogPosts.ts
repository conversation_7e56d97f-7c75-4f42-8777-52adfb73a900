import { CollectionConfig } from 'payload'

export const BlogPosts: CollectionConfig = {
  slug: 'blog-posts',
  labels: {
    singular: 'Blog Post',
    plural: 'Blog Posts',
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'author_id', 'is_published', 'published_at', 'category'],
    group: 'Content',
  },
  access: {
    read: () => true,
    create: () => true,
    update: () => true,
    delete: () => true,
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
    },
    {
      name: 'content',
      type: 'richText',
      required: true,
    },
    {
      name: 'excerpt',
      type: 'textarea',
    },
    {
      name: 'author_id',
      type: 'text',
      label: 'Author ID',
    },
    {
      name: 'category',
      type: 'text',
    },
    {
      name: 'tags',
      type: 'text',
    },
    {
      name: 'is_published',
      type: 'checkbox',
      defaultValue: false,
    },
    {
      name: 'is_featured',
      type: 'checkbox',
      defaultValue: false,
    },
    {
      name: 'published_at',
      type: 'date',
    },
    {
      name: 'featured_image',
      type: 'text',
    },
    {
      name: 'seo_title',
      type: 'text',
    },
    {
      name: 'seo_description',
      type: 'text',
    },
    {
      name: 'language',
      type: 'text',
      defaultValue: 'th',
    },
  ],
}
