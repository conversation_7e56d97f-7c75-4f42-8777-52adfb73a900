import { CollectionConfig } from 'payload'

export const BlogPosts: CollectionConfig = {
  slug: 'blog-posts',
  labels: {
    singular: 'Blog Post',
    plural: 'Blog Posts',
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'author_id', 'is_published', 'published_at', 'category'],
    group: 'Content',
  },
  access: {
    read: () => true,
    create: () => true,
    update: () => true,
    delete: () => true,
  },
  hooks: {
    beforeChange: [
      ({ data }) => {
        // Convert comma-separated strings to PostgreSQL array format
        if (data.tags && typeof data.tags === 'string') {
          const tagsArray = data.tags
            .split(',')
            .map((tag) => tag.trim())
            .filter((tag) => tag.length > 0)
          data.tags = `{${tagsArray.join(',')}}`
        }
        if (data.gallery_images && typeof data.gallery_images === 'string') {
          const imagesArray = data.gallery_images
            .split(',')
            .map((img) => img.trim())
            .filter((img) => img.length > 0)
          data.gallery_images = `{${imagesArray.join(',')}}`
        }
        if (data.seo_keywords && typeof data.seo_keywords === 'string') {
          const keywordsArray = data.seo_keywords
            .split(',')
            .map((keyword) => keyword.trim())
            .filter((keyword) => keyword.length > 0)
          data.seo_keywords = `{${keywordsArray.join(',')}}`
        }
        return data
      },
    ],
    afterRead: [
      ({ data }) => {
        // Handle both single documents and arrays of documents
        const processDocument = (doc) => {
          if (!doc) return doc

          // Convert PostgreSQL array format back to comma-separated strings for display
          if (doc.tags && typeof doc.tags === 'string' && doc.tags.startsWith('{')) {
            doc.tags = doc.tags.slice(1, -1).split(',').join(', ')
          }
          if (
            doc.gallery_images &&
            typeof doc.gallery_images === 'string' &&
            doc.gallery_images.startsWith('{')
          ) {
            doc.gallery_images = doc.gallery_images.slice(1, -1).split(',').join(', ')
          }
          if (
            doc.seo_keywords &&
            typeof doc.seo_keywords === 'string' &&
            doc.seo_keywords.startsWith('{')
          ) {
            doc.seo_keywords = doc.seo_keywords.slice(1, -1).split(',').join(', ')
          }
          return doc
        }

        if (Array.isArray(data)) {
          return data.map(processDocument)
        } else {
          return processDocument(data)
        }
      },
    ],
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
    },
    {
      name: 'content',
      type: 'richText',
      required: true,
    },
    {
      name: 'excerpt',
      type: 'textarea',
    },
    {
      name: 'author_id',
      type: 'text',
      label: 'Author ID',
    },
    {
      name: 'category',
      type: 'text',
    },
    {
      name: 'tags',
      type: 'text',
      admin: {
        description: 'Enter tags separated by commas (e.g., skincare, beauty, health)',
      },
    },
    {
      name: 'is_published',
      type: 'checkbox',
      defaultValue: false,
    },
    {
      name: 'is_featured',
      type: 'checkbox',
      defaultValue: false,
    },
    {
      name: 'published_at',
      type: 'date',
    },
    {
      name: 'featured_image',
      type: 'text',
    },
    {
      name: 'gallery_images',
      type: 'text',
      admin: {
        description: 'Enter image URLs separated by commas',
      },
    },
    {
      name: 'seo_title',
      type: 'text',
    },
    {
      name: 'seo_description',
      type: 'text',
    },
    {
      name: 'seo_keywords',
      type: 'text',
      admin: {
        description: 'Enter SEO keywords separated by commas',
      },
    },
    {
      name: 'difficulty',
      type: 'select',
      options: [
        { label: 'Beginner', value: 'beginner' },
        { label: 'Intermediate', value: 'intermediate' },
        { label: 'Advanced', value: 'advanced' },
      ],
      defaultValue: 'beginner',
    },
    {
      name: 'read_time_minutes',
      type: 'number',
      defaultValue: 5,
    },
    {
      name: 'view_count',
      type: 'number',
      defaultValue: 0,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'like_count',
      type: 'number',
      defaultValue: 0,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'language',
      type: 'text',
      defaultValue: 'th',
    },
  ],
}
