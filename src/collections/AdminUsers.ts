import { CollectionConfig } from 'payload'
import { getDatabaseFields } from '../lib/supabaseAdapter'

export const AdminUsers: CollectionConfig = {
  slug: 'admin-users',
  labels: {
    singular: 'Admin User',
    plural: 'Admin Users',
  },
  admin: {
    useAsTitle: 'role',
    defaultColumns: ['role', 'department', 'employee_id', 'is_active'],
    group: 'Users',
  },
  access: {
    read: () => true,
    create: () => true,
    update: () => true,
    delete: () => true,
  },
  fields: getDatabaseFields('admin_users'),
}
