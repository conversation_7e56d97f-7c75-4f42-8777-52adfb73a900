import { createClient } from '@supabase/supabase-js'

// Create Supabase client with service role key for admin operations
const supabaseAdmin = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export interface SupabaseAuthUser {
  id: string
  email: string
  email_confirmed_at: string | null
  created_at: string
  updated_at: string
  last_sign_in_at: string | null
  app_metadata: Record<string, any>
  user_metadata: Record<string, any>
}

/**
 * Verify if a user exists in Supabase Auth database
 * @param email - User email to verify
 * @returns Promise<boolean> - True if user exists and is verified
 */
export async function verifyUserInSupabaseAuth(email: string): Promise<boolean> {
  try {
    console.log(`🔍 Verifying user in Supabase Auth: ${email}`)
    
    // Get user by email from Supabase Auth
    const { data: { users }, error } = await supabaseAdmin.auth.admin.listUsers({
      page: 1,
      perPage: 1000 // Adjust as needed
    })

    if (error) {
      console.error('❌ Error fetching users from Supabase Auth:', error)
      return false
    }

    // Find user by email
    const user = users.find(u => u.email?.toLowerCase() === email.toLowerCase())
    
    if (!user) {
      console.log(`❌ User not found in Supabase Auth: ${email}`)
      return false
    }

    // Check if user email is confirmed
    if (!user.email_confirmed_at) {
      console.log(`❌ User email not confirmed in Supabase Auth: ${email}`)
      return false
    }

    console.log(`✅ User verified in Supabase Auth: ${email}`)
    return true
  } catch (error) {
    console.error('❌ Error verifying user in Supabase Auth:', error)
    return false
  }
}

/**
 * Get user details from Supabase Auth database
 * @param email - User email to get details for
 * @returns Promise<SupabaseAuthUser | null> - User details or null if not found
 */
export async function getSupabaseAuthUser(email: string): Promise<SupabaseAuthUser | null> {
  try {
    console.log(`🔍 Getting user details from Supabase Auth: ${email}`)
    
    // Get user by email from Supabase Auth
    const { data: { users }, error } = await supabaseAdmin.auth.admin.listUsers({
      page: 1,
      perPage: 1000 // Adjust as needed
    })

    if (error) {
      console.error('❌ Error fetching users from Supabase Auth:', error)
      return null
    }

    // Find user by email
    const user = users.find(u => u.email?.toLowerCase() === email.toLowerCase())
    
    if (!user) {
      console.log(`❌ User not found in Supabase Auth: ${email}`)
      return null
    }

    console.log(`✅ User found in Supabase Auth: ${email}`)
    return {
      id: user.id,
      email: user.email!,
      email_confirmed_at: user.email_confirmed_at,
      created_at: user.created_at,
      updated_at: user.updated_at,
      last_sign_in_at: user.last_sign_in_at,
      app_metadata: user.app_metadata,
      user_metadata: user.user_metadata
    }
  } catch (error) {
    console.error('❌ Error getting user from Supabase Auth:', error)
    return null
  }
}

/**
 * Check if user is allowed to register (exists in Supabase Auth)
 * @param email - User email to check
 * @returns Promise<{ allowed: boolean, reason?: string }> - Registration permission result
 */
export async function checkRegistrationPermission(email: string): Promise<{ allowed: boolean, reason?: string }> {
  try {
    const user = await getSupabaseAuthUser(email)
    
    if (!user) {
      return {
        allowed: false,
        reason: 'User not found in authorized user database. Please contact an administrator to be added to the system.'
      }
    }

    if (!user.email_confirmed_at) {
      return {
        allowed: false,
        reason: 'User email not confirmed in the authorization system. Please verify your email first.'
      }
    }

    return { allowed: true }
  } catch (error) {
    console.error('❌ Error checking registration permission:', error)
    return {
      allowed: false,
      reason: 'Unable to verify user authorization. Please try again later.'
    }
  }
}

/**
 * Check if user is allowed to login (exists and is verified in Supabase Auth)
 * @param email - User email to check
 * @returns Promise<{ allowed: boolean, reason?: string }> - Login permission result
 */
export async function checkLoginPermission(email: string): Promise<{ allowed: boolean, reason?: string }> {
  try {
    const user = await getSupabaseAuthUser(email)
    
    if (!user) {
      return {
        allowed: false,
        reason: 'User not found in authorized user database. Access denied.'
      }
    }

    if (!user.email_confirmed_at) {
      return {
        allowed: false,
        reason: 'User email not confirmed. Please verify your email before logging in.'
      }
    }

    return { allowed: true }
  } catch (error) {
    console.error('❌ Error checking login permission:', error)
    return {
      allowed: false,
      reason: 'Unable to verify user authorization. Please try again later.'
    }
  }
}

export { supabaseAdmin }
