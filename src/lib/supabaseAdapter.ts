import { Field } from 'payload'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import { createClient } from '@supabase/supabase-js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

export const supabase = createClient(process.env.SUPABASE_URL!, process.env.SUPABASE_ANON_KEY!)

interface ColumnInfo {
  column_name: string
  data_type: string
  is_nullable: string
  column_default: string | null
  character_maximum_length: number | null
}

export function mapPostgresToPayloadField(column: ColumnInfo): Field {
  const { column_name, data_type, is_nullable, column_default } = column

  const baseField = {
    name: column_name,
    required: is_nullable === 'NO' && !column_default,
  }

  switch (data_type) {
    case 'integer':
    case 'bigint':
    case 'smallint':
    case 'numeric':
    case 'decimal':
    case 'real':
    case 'double precision':
      return { ...baseField, type: 'number' } as Field

    case 'boolean':
      return { ...baseField, type: 'checkbox' } as Field

    case 'timestamp with time zone':
    case 'timestamp without time zone':
    case 'date':
      return { ...baseField, type: 'date' } as Field

    case 'jsonb':
    case 'json':
      return { ...baseField, type: 'json' } as Field

    case 'uuid':
      return { ...baseField, type: 'text', admin: { hidden: column_name === 'id' } } as Field

    default:
      break
  }

  if (column_name === 'id') {
    return { ...baseField, type: 'text', admin: { hidden: true } } as Field
  }

  if (column_name.includes('email')) {
    return { ...baseField, type: 'email' } as Field
  }

  if (
    column_name.includes('password') ||
    column_name.includes('hash') ||
    column_name.includes('salt')
  ) {
    return { ...baseField, type: 'text', admin: { hidden: true } } as Field
  }

  return { ...baseField, type: 'text' } as Field
}

export function generatePayloadFields(columns: ColumnInfo[]): Field[] {
  return columns
    .filter((col) => !['created_at', 'updated_at'].includes(col.column_name))
    .map(mapPostgresToPayloadField)
}

const slugToTableMap: Record<string, string> = {
  appointments: 'appointments',
  doctors: 'doctors',
  services: 'services',
  'time-slots': 'time_slots',
  'clinic-settings': 'clinic_settings',
  'user-profiles': 'user_profiles',
  'admin-profiles': 'admin_profiles',
  'admin-users': 'admin_users',
  invoices: 'invoices',
  payments: 'payments',
  promotions: 'promotions',
  'inventory-items': 'inventory_items',
  'blog-posts': 'blog_posts',
  'cms-pages': 'cms_pages',
  'marketing-campaigns': 'marketing_campaigns',
  reviews: 'reviews',
  'analytics-events': 'analytics_events',
  'kpi-metrics': 'kpi_metrics',
}

export function getDatabaseFields(slug: string): Field[] {
  const tableName = slugToTableMap[slug] || slug
  try {
    const schemaPath = path.join(__dirname, '..', '_schema', `${tableName}.json`)
    const schemaContent = fs.readFileSync(schemaPath, 'utf8')
    const schema = JSON.parse(schemaContent)
    return schema.fields
  } catch (error) {
    console.warn(`Schema file not found for ${tableName}, using default fields`)
    return [
      {
        name: 'id',
        type: 'number',
        required: true,
      },
    ]
  }
}
