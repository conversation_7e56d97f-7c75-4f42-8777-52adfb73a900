import { pgTable, serial, text, timestamp, boolean, integer, jsonb } from 'drizzle-orm/pg-core'

// Basic schema for Payload CMS tables
export const users = pgTable('users', {
  id: serial('id').primaryKey(),
  email: text('email').notNull().unique(),
  password: text('password'),
  salt: text('salt'),
  loginAttempts: integer('loginAttempts'),
  lockUntil: timestamp('lockUntil'),
  createdAt: timestamp('createdAt').defaultNow(),
  updatedAt: timestamp('updatedAt').defaultNow(),
})

export const media = pgTable('media', {
  id: serial('id').primaryKey(),
  alt: text('alt'),
  filename: text('filename'),
  mimeType: text('mimeType'),
  filesize: integer('filesize'),
  width: integer('width'),
  height: integer('height'),
  focalX: integer('focalX'),
  focalY: integer('focalY'),
  url: text('url'),
  thumbnailURL: text('thumbnailURL'),
  createdAt: timestamp('createdAt').defaultNow(),
  updatedAt: timestamp('updatedAt').defaultNow(),
})

export const payloadPreferences = pgTable('payload_preferences', {
  id: serial('id').primaryKey(),
  key: text('key'),
  value: jsonb('value'),
  user: integer('user').references(() => users.id),
  createdAt: timestamp('createdAt').defaultNow(),
  updatedAt: timestamp('updatedAt').defaultNow(),
})

export const payloadMigrations = pgTable('payload_migrations', {
  id: serial('id').primaryKey(),
  name: text('name'),
  batch: integer('batch'),
  createdAt: timestamp('createdAt').defaultNow(),
  updatedAt: timestamp('updatedAt').defaultNow(),
})
